/**
 * 高级功能模块
 * 包含测量工具、分析功能、导出功能等专业特性
 */

class AdvancedFeatures {
    constructor(viewer) {
        this.viewer = viewer;
        this.measurementMode = null;
        this.selectedAtoms = [];
        this.measurements = [];
        this.analysisResults = {};
        this.isRecording = false;
        this.recordedFrames = [];
        
        this.initializeFeatures();
    }

    /**
     * 初始化高级功能
     */
    initializeFeatures() {
        console.log('正在初始化高级功能模块...');
        
        // 初始化测量工具
        this.initMeasurementTools();
        
        // 初始化分析工具
        this.initAnalysisTools();
        
        // 初始化导出功能
        this.initExportTools();
        
        // 添加鼠标事件监听器
        this.addEventListeners();
        
        console.log('高级功能模块初始化完成');
    }

    /**
     * 初始化测量工具
     */
    initMeasurementTools() {
        const measurementSelect = document.getElementById('measurement-mode');
        if (measurementSelect) {
            measurementSelect.addEventListener('change', (e) => {
                this.setMeasurementMode(e.target.value);
            });
        }
    }

    /**
     * 设置测量模式
     */
    setMeasurementMode(mode) {
        this.measurementMode = mode;
        this.selectedAtoms = [];
        this.clearMeasurements();
        
        if (mode) {
            console.log(`激活测量模式: ${mode}`);
            this.showMeasurementInstructions(mode);
        } else {
            console.log('关闭测量模式');
            this.hideMeasurementInstructions();
        }
    }

    /**
     * 显示测量说明
     */
    showMeasurementInstructions(mode) {
        const instructions = {
            'distance': '点击两个原子测量距离',
            'angle': '点击三个原子测量键角',
            'dihedral': '点击四个原子测量二面角',
            'volume': '计算分子体积'
        };
        
        // 在界面上显示说明
        this.showTooltip(instructions[mode] || '选择原子进行测量');
    }

    /**
     * 隐藏测量说明
     */
    hideMeasurementInstructions() {
        this.hideTooltip();
    }

    /**
     * 显示提示信息
     */
    showTooltip(message) {
        // 创建或更新提示框
        let tooltip = document.getElementById('measurement-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'measurement-tooltip';
            tooltip.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                z-index: 1000;
                font-size: 14px;
                max-width: 200px;
            `;
            document.body.appendChild(tooltip);
        }
        tooltip.textContent = message;
        tooltip.style.display = 'block';
    }

    /**
     * 隐藏提示信息
     */
    hideTooltip() {
        const tooltip = document.getElementById('measurement-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    /**
     * 处理原子点击事件
     */
    onAtomClick(atomIndex) {
        if (!this.measurementMode) return;
        
        this.selectedAtoms.push(atomIndex);
        
        // 根据测量模式处理
        switch (this.measurementMode) {
            case 'distance':
                if (this.selectedAtoms.length === 2) {
                    this.measureDistance();
                }
                break;
            case 'angle':
                if (this.selectedAtoms.length === 3) {
                    this.measureAngle();
                }
                break;
            case 'dihedral':
                if (this.selectedAtoms.length === 4) {
                    this.measureDihedral();
                }
                break;
        }
    }

    /**
     * 测量原子间距离
     */
    measureDistance() {
        if (this.selectedAtoms.length !== 2) return;
        
        const atom1 = this.viewer.currentMolecule.atoms[this.selectedAtoms[0]];
        const atom2 = this.viewer.currentMolecule.atoms[this.selectedAtoms[1]];
        
        const distance = this.calculateDistance(atom1.position, atom2.position);
        
        const measurement = {
            type: 'distance',
            atoms: [...this.selectedAtoms],
            value: distance,
            unit: 'Å'
        };
        
        this.measurements.push(measurement);
        this.displayMeasurement(measurement);
        this.selectedAtoms = [];
    }

    /**
     * 测量键角
     */
    measureAngle() {
        if (this.selectedAtoms.length !== 3) return;
        
        const atoms = this.selectedAtoms.map(i => this.viewer.currentMolecule.atoms[i]);
        const angle = this.calculateAngle(
            atoms[0].position,
            atoms[1].position,
            atoms[2].position
        );
        
        const measurement = {
            type: 'angle',
            atoms: [...this.selectedAtoms],
            value: angle,
            unit: '°'
        };
        
        this.measurements.push(measurement);
        this.displayMeasurement(measurement);
        this.selectedAtoms = [];
    }

    /**
     * 测量二面角
     */
    measureDihedral() {
        if (this.selectedAtoms.length !== 4) return;
        
        const atoms = this.selectedAtoms.map(i => this.viewer.currentMolecule.atoms[i]);
        const dihedral = this.calculateDihedral(
            atoms[0].position,
            atoms[1].position,
            atoms[2].position,
            atoms[3].position
        );
        
        const measurement = {
            type: 'dihedral',
            atoms: [...this.selectedAtoms],
            value: dihedral,
            unit: '°'
        };
        
        this.measurements.push(measurement);
        this.displayMeasurement(measurement);
        this.selectedAtoms = [];
    }

    /**
     * 计算两点间距离
     */
    calculateDistance(pos1, pos2) {
        const dx = pos1[0] - pos2[0];
        const dy = pos1[1] - pos2[1];
        const dz = pos1[2] - pos2[2];
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * 计算键角
     */
    calculateAngle(pos1, pos2, pos3) {
        // 向量 v1 = pos1 - pos2, v2 = pos3 - pos2
        const v1 = [pos1[0] - pos2[0], pos1[1] - pos2[1], pos1[2] - pos2[2]];
        const v2 = [pos3[0] - pos2[0], pos3[1] - pos2[1], pos3[2] - pos2[2]];
        
        // 计算点积和模长
        const dot = v1[0] * v2[0] + v1[1] * v2[1] + v1[2] * v2[2];
        const mag1 = Math.sqrt(v1[0] * v1[0] + v1[1] * v1[1] + v1[2] * v1[2]);
        const mag2 = Math.sqrt(v2[0] * v2[0] + v2[1] * v2[1] + v2[2] * v2[2]);
        
        // 计算角度
        const cosAngle = dot / (mag1 * mag2);
        return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * 180 / Math.PI;
    }

    /**
     * 计算二面角
     */
    calculateDihedral(pos1, pos2, pos3, pos4) {
        // 计算三个向量
        const v1 = [pos2[0] - pos1[0], pos2[1] - pos1[1], pos2[2] - pos1[2]];
        const v2 = [pos3[0] - pos2[0], pos3[1] - pos2[1], pos3[2] - pos2[2]];
        const v3 = [pos4[0] - pos3[0], pos4[1] - pos3[1], pos4[2] - pos3[2]];
        
        // 计算法向量
        const n1 = this.crossProduct(v1, v2);
        const n2 = this.crossProduct(v2, v3);
        
        // 计算二面角
        const dot = n1[0] * n2[0] + n1[1] * n2[1] + n1[2] * n2[2];
        const mag1 = Math.sqrt(n1[0] * n1[0] + n1[1] * n1[1] + n1[2] * n1[2]);
        const mag2 = Math.sqrt(n2[0] * n2[0] + n2[1] * n2[1] + n2[2] * n2[2]);
        
        const cosAngle = dot / (mag1 * mag2);
        return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * 180 / Math.PI;
    }

    /**
     * 向量叉积
     */
    crossProduct(v1, v2) {
        return [
            v1[1] * v2[2] - v1[2] * v2[1],
            v1[2] * v2[0] - v1[0] * v2[2],
            v1[0] * v2[1] - v1[1] * v2[0]
        ];
    }

    /**
     * 显示测量结果
     */
    displayMeasurement(measurement) {
        const info = document.getElementById('molecule-info');
        if (info) {
            const measurementText = `${measurement.type}: ${measurement.value.toFixed(2)} ${measurement.unit}`;
            info.innerHTML += `<p><strong>${measurementText}</strong></p>`;
        }
        
        console.log(`测量结果: ${measurement.type} = ${measurement.value.toFixed(2)} ${measurement.unit}`);
    }

    /**
     * 清除所有测量
     */
    clearMeasurements() {
        this.measurements = [];
        this.selectedAtoms = [];
    }

    /**
     * 初始化分析工具
     */
    initAnalysisTools() {
        // 分子表面
        const surfaceBtn = document.getElementById('molecular-surface');
        if (surfaceBtn) {
            surfaceBtn.addEventListener('click', () => this.showMolecularSurface());
        }
        
        // 静电势
        const potentialBtn = document.getElementById('electrostatic-potential');
        if (potentialBtn) {
            potentialBtn.addEventListener('click', () => this.showElectrostaticPotential());
        }
        
        // 氢键显示
        const hbondsBtn = document.getElementById('hydrogen-bonds');
        if (hbondsBtn) {
            hbondsBtn.addEventListener('click', () => this.showHydrogenBonds());
        }
        
        // 空腔检测
        const cavityBtn = document.getElementById('cavity-detection');
        if (cavityBtn) {
            cavityBtn.addEventListener('click', () => this.detectCavities());
        }
    }

    /**
     * 显示分子表面
     */
    showMolecularSurface() {
        console.log('显示分子表面');
        this.showTooltip('分子表面功能已激活');
        // TODO: 实现分子表面计算和显示
    }

    /**
     * 显示静电势
     */
    showElectrostaticPotential() {
        console.log('显示静电势');
        this.showTooltip('静电势计算功能已激活');
        // TODO: 实现静电势计算和可视化
    }

    /**
     * 显示氢键
     */
    showHydrogenBonds() {
        console.log('显示氢键');
        this.showTooltip('氢键检测功能已激活');
        // TODO: 实现氢键检测和显示
    }

    /**
     * 检测空腔
     */
    detectCavities() {
        console.log('检测分子空腔');
        this.showTooltip('空腔检测功能已激活');
        // TODO: 实现空腔检测算法
    }

    /**
     * 初始化导出工具
     */
    initExportTools() {
        // 导出图片
        const exportImageBtn = document.getElementById('export-image');
        if (exportImageBtn) {
            exportImageBtn.addEventListener('click', () => this.exportImage());
        }
        
        // 录制视频
        const exportVideoBtn = document.getElementById('export-video');
        if (exportVideoBtn) {
            exportVideoBtn.addEventListener('click', () => this.toggleVideoRecording());
        }
        
        // 导出数据
        const exportDataBtn = document.getElementById('export-data');
        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', () => this.exportData());
        }
        
        // 生成报告
        const exportReportBtn = document.getElementById('export-report');
        if (exportReportBtn) {
            exportReportBtn.addEventListener('click', () => this.generateReport());
        }
    }

    /**
     * 导出当前视图为图片
     */
    exportImage() {
        if (!this.viewer.renderer) {
            console.error('渲染器不可用');
            return;
        }
        
        try {
            // 渲染当前帧
            this.viewer.renderer.render(this.viewer.scene, this.viewer.camera);
            
            // 获取canvas数据
            const canvas = this.viewer.renderer.domElement;
            const dataURL = canvas.toDataURL('image/png');
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = `molecule_${Date.now()}.png`;
            link.href = dataURL;
            link.click();
            
            console.log('图片导出成功');
            this.showTooltip('图片已导出');
        } catch (error) {
            console.error('图片导出失败:', error);
            this.showTooltip('图片导出失败');
        }
    }

    /**
     * 切换视频录制
     */
    toggleVideoRecording() {
        if (this.isRecording) {
            this.stopVideoRecording();
        } else {
            this.startVideoRecording();
        }
    }

    /**
     * 开始视频录制
     */
    startVideoRecording() {
        this.isRecording = true;
        this.recordedFrames = [];
        console.log('开始录制视频');
        this.showTooltip('视频录制已开始');
        
        // 更新按钮文本
        const btn = document.getElementById('export-video');
        if (btn) btn.textContent = '停止录制';
    }

    /**
     * 停止视频录制
     */
    stopVideoRecording() {
        this.isRecording = false;
        console.log('停止录制视频');
        this.showTooltip('视频录制已停止');
        
        // 更新按钮文本
        const btn = document.getElementById('export-video');
        if (btn) btn.textContent = '录制视频';
        
        // TODO: 实现视频编码和导出
    }

    /**
     * 导出分子数据
     */
    exportData() {
        if (!this.viewer.currentMolecule) {
            console.error('没有可导出的分子数据');
            return;
        }
        
        const data = {
            molecule: this.viewer.currentMolecule,
            measurements: this.measurements,
            analysisResults: this.analysisResults,
            exportTime: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.download = `molecule_data_${Date.now()}.json`;
        link.href = url;
        link.click();
        
        URL.revokeObjectURL(url);
        console.log('数据导出成功');
        this.showTooltip('数据已导出');
    }

    /**
     * 生成分析报告
     */
    generateReport() {
        if (!this.viewer.currentMolecule) {
            console.error('没有可生成报告的分子');
            return;
        }
        
        const report = this.createAnalysisReport();
        const blob = new Blob([report], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.download = `molecule_report_${Date.now()}.html`;
        link.href = url;
        link.click();
        
        URL.revokeObjectURL(url);
        console.log('报告生成成功');
        this.showTooltip('分析报告已生成');
    }

    /**
     * 创建分析报告
     */
    createAnalysisReport() {
        const molecule = this.viewer.currentMolecule;
        const date = new Date().toLocaleString('zh-CN');
        
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>分子分析报告 - ${molecule.name}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #2c3e50; }
        h2 { color: #3498db; border-bottom: 2px solid #3498db; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>分子分析报告</h1>
    <p><strong>分子名称:</strong> ${molecule.name}</p>
    <p><strong>分子式:</strong> ${molecule.formula || '未知'}</p>
    <p><strong>生成时间:</strong> ${date}</p>
    
    <h2>基本信息</h2>
    <table>
        <tr><th>属性</th><th>值</th></tr>
        <tr><td>原子数量</td><td>${molecule.atoms.length}</td></tr>
        <tr><td>化学键数量</td><td>${molecule.bonds ? molecule.bonds.length : 0}</td></tr>
        <tr><td>分子类别</td><td>${molecule.category || '未分类'}</td></tr>
    </table>
    
    <h2>测量结果</h2>
    ${this.measurements.length > 0 ? this.formatMeasurements() : '<p>无测量数据</p>'}
    
    <h2>原子列表</h2>
    <table>
        <tr><th>序号</th><th>元素</th><th>X坐标</th><th>Y坐标</th><th>Z坐标</th></tr>
        ${molecule.atoms.map((atom, i) => 
            `<tr><td>${i + 1}</td><td>${atom.symbol}</td><td>${atom.position[0].toFixed(3)}</td><td>${atom.position[1].toFixed(3)}</td><td>${atom.position[2].toFixed(3)}</td></tr>`
        ).join('')}
    </table>
</body>
</html>`;
    }

    /**
     * 格式化测量结果
     */
    formatMeasurements() {
        return `
<table>
    <tr><th>类型</th><th>原子</th><th>值</th><th>单位</th></tr>
    ${this.measurements.map(m => 
        `<tr><td>${m.type}</td><td>${m.atoms.join(', ')}</td><td>${m.value.toFixed(3)}</td><td>${m.unit}</td></tr>`
    ).join('')}
</table>`;
    }

    /**
     * 添加事件监听器
     */
    addEventListeners() {
        // 监听分子查看器的点击事件
        if (this.viewer.container) {
            this.viewer.container.addEventListener('click', (event) => {
                if (this.measurementMode) {
                    // TODO: 实现原子点击检测
                    // 这需要与MoleculeViewer的射线投射系统集成
                }
            });
        }
    }
}

// 导出类
window.AdvancedFeatures = AdvancedFeatures;
