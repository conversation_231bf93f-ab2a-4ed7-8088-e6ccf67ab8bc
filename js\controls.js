/**
 * 控制面板交互逻辑
 * 增强版 - 支持更多控制选项和交互功能
 */
class Controls {
    /**
     * 构造函数
     * @param {MoleculeViewer} viewer - 分子可视化器实例
     * @param {MoleculeLoader} loader - 分子加载器实例
     */
    constructor(viewer, loader) {
        this.viewer = viewer;
        this.loader = loader;

        // 获取控制元素
        this.moleculeSelect = document.getElementById('molecule-select');
        this.displayModeSelect = document.getElementById('display-mode');
        this.animationTypeSelect = document.getElementById('animation-type');
        this.animationSpeedSlider = document.getElementById('animation-speed');
        this.playPauseButton = document.getElementById('play-pause');
        this.resetButton = document.getElementById('reset');
        this.moleculeInfo = document.getElementById('molecule-info');

        // PDB加载控制元素
        this.pdbIdInput = document.getElementById('pdb-id');
        this.loadPdbButton = document.getElementById('load-pdb');

        // 新增控制元素
        this.qualityToggle = document.getElementById('quality-toggle');
        this.effectsToggle = document.getElementById('effects-toggle');

        // 化学反应控制元素
        this.reactionSelect = document.getElementById('reaction-select');
        this.reactionInfo = document.getElementById('reaction-info');
        this.startReactionButton = document.getElementById('start-reaction');
        this.stopReactionButton = document.getElementById('stop-reaction');
        this.resetReactionButton = document.getElementById('reset-reaction');
        this.reactionSpeedSlider = document.getElementById('reaction-speed');
        this.reactionSpeedValue = document.getElementById('reaction-speed-value');
        this.reactionProgressFill = document.getElementById('reaction-progress-fill');
        this.reactionProgressText = document.getElementById('reaction-progress-text');

        // 对话框元素
        this.aboutLink = document.getElementById('about-link');
        this.helpLink = document.getElementById('help-link');
        this.changelogLink = document.getElementById('changelog-link');
        this.aboutDialog = document.getElementById('about-dialog');
        this.helpDialog = document.getElementById('help-dialog');
        this.changelogDialog = document.getElementById('changelog-dialog');
        this.closeButtons = document.querySelectorAll('.close-button');

        // 初始化控制器
        this._initControls();
    }

    /**
     * 初始化控制器
     * @private
     */
    _initControls() {
        // 填充分子选择下拉框
        this._populateMoleculeSelect();

        // 添加基本控制事件监听器
        this.moleculeSelect.addEventListener('change', () => this._onMoleculeChange());
        this.displayModeSelect.addEventListener('change', () => this._onDisplayModeChange());
        if (this.animationTypeSelect) {
            this.animationTypeSelect.addEventListener('change', () => this._onAnimationTypeChange());
        }
        this.animationSpeedSlider.addEventListener('input', () => this._onAnimationSpeedChange());
        this.playPauseButton.addEventListener('click', () => this._onPlayPauseClick());
        this.resetButton.addEventListener('click', () => this._onResetClick());

        // 添加PDB加载事件监听器
        if (this.loadPdbButton && this.pdbIdInput) {
            this.loadPdbButton.addEventListener('click', () => this._onLoadPdbClick());
            this.pdbIdInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this._onLoadPdbClick();
                }
            });
        }

        // 添加新控制事件监听器
        if (this.qualityToggle) {
            this.qualityToggle.addEventListener('change', () => this._onQualityToggle());
        }

        if (this.effectsToggle) {
            this.effectsToggle.addEventListener('change', () => this._onEffectsToggle());
        }

        // 添加化学反应事件监听器
        if (this.reactionSelect) {
            this.reactionSelect.addEventListener('change', () => this._onReactionSelectChange());
        }

        if (this.startReactionButton) {
            this.startReactionButton.addEventListener('click', () => this._onStartReaction());
        }

        if (this.stopReactionButton) {
            this.stopReactionButton.addEventListener('click', () => this._onStopReaction());
        }

        if (this.resetReactionButton) {
            this.resetReactionButton.addEventListener('click', () => this._onResetReaction());
        }

        if (this.reactionSpeedSlider) {
            this.reactionSpeedSlider.addEventListener('input', () => this._onReactionSpeedChange());
        }

        // 添加对话框事件监听器
        this._initDialogs();

        // 加载默认分子
        this._loadDefaultMolecule();
    }

    /**
     * 初始化对话框
     * @private
     */
    _initDialogs() {
        // 关于对话框
        if (this.aboutLink && this.aboutDialog) {
            this.aboutLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.aboutDialog.classList.add('active');
            });
        }

        // 帮助对话框
        if (this.helpLink && this.helpDialog) {
            this.helpLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.helpDialog.classList.add('active');
            });
        }

        // 更新日志对话框
        if (this.changelogLink && this.changelogDialog) {
            this.changelogLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.changelogDialog.classList.add('active');
            });
        }

        // 关闭按钮
        this.closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const dialog = button.closest('.dialog');
                if (dialog) {
                    dialog.classList.remove('active');
                }
            });
        });

        // 点击对话框背景关闭
        document.querySelectorAll('.dialog').forEach(dialog => {
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    dialog.classList.remove('active');
                }
            });
        });
    }

    /**
     * 填充分子选择下拉框
     * @private
     */
    _populateMoleculeSelect() {
        const molecules = this.loader.getAvailableMolecules();

        // 清空现有选项
        this.moleculeSelect.innerHTML = '';

        // 添加新选项
        molecules.forEach(moleculeId => {
            const molecule = this.loader.getMolecule(moleculeId);
            const option = document.createElement('option');
            option.value = moleculeId;
            option.textContent = molecule.name;
            this.moleculeSelect.appendChild(option);
        });
    }

    /**
     * 加载默认分子
     * @private
     */
    _loadDefaultMolecule() {
        console.log('开始加载默认分子...');
        console.log('分子选择器选项数量:', this.moleculeSelect.options.length);

        if (this.moleculeSelect.options.length > 0) {
            const defaultMoleculeId = this.moleculeSelect.options[0].value;
            console.log('默认分子ID:', defaultMoleculeId);
            this._loadMolecule(defaultMoleculeId);
        } else {
            console.warn('没有可用的分子选项');
        }
    }

    /**
     * 加载指定的分子
     * @private
     * @param {string} moleculeId - 分子ID
     */
    _loadMolecule(moleculeId) {
        console.log('正在加载分子:', moleculeId);

        // 检查是否是PDB格式的分子ID (以"pdb:"开头)
        if (moleculeId.startsWith('pdb:')) {
            const pdbId = moleculeId.substring(4); // 去掉"pdb:"前缀
            console.log('检测到PDB分子，ID:', pdbId);
            this._loadPdbMolecule(pdbId);
            return;
        }

        const molecule = this.loader.getMolecule(moleculeId);
        console.log('从加载器获取的分子数据:', molecule);

        if (molecule) {
            console.log('分子数据有效，开始加载到查看器...');

            // 加载分子
            this.viewer.loadMolecule(molecule);
            console.log('分子已发送到查看器');

            // 如果有动画类型选择器，设置动画类型
            if (this.animationTypeSelect) {
                console.log('设置动画类型...');
                this._onAnimationTypeChange();
            }

            // 更新分子信息
            console.log('更新分子信息...');
            this.moleculeInfo.innerHTML = this.loader.getMoleculeInfoHTML(moleculeId);
            console.log('分子信息已更新');
        } else {
            console.error('无法获取分子数据，moleculeId:', moleculeId);
        }
    }

    /**
     * 加载PDB分子
     * @private
     * @param {string} pdbId - PDB ID
     */
    _loadPdbMolecule(pdbId) {
        // 显示加载状态
        this.moleculeInfo.innerHTML = `<p>正在从PDB数据库加载 ${pdbId}...</p>`;

        // 禁用加载按钮
        if (this.loadPdbButton) {
            this.loadPdbButton.disabled = true;
            this.loadPdbButton.textContent = '加载中...';
        }

        // 加载PDB分子
        this.loader.loadPDB(pdbId, {
            pdbId: pdbId,
            name: `PDB: ${pdbId}`,
            description: `从蛋白质数据库加载的分子结构 (ID: ${pdbId})`,
            url: `https://www.rcsb.org/structure/${pdbId}`
        })
        .then(molecule => {
            console.log(`PDB分子 ${pdbId} 加载成功:`, molecule);

            // 加载分子
            this.viewer.loadMolecule(molecule);

            // 如果有动画类型选择器，设置动画类型
            if (this.animationTypeSelect) {
                this._onAnimationTypeChange();
            }

            // 更新分子信息
            let infoHTML = `
                <h3>${molecule.name}</h3>
                <p><strong>化学式:</strong> ${molecule.formula}</p>
                <p>${molecule.description}</p>
                <p><strong>原子数:</strong> ${molecule.atoms.length}</p>
                <p><strong>键数:</strong> ${molecule.bonds.length}</p>
            `;

            // 添加PDB特定信息
            if (molecule.pdbInfo) {
                infoHTML += `
                    <p><strong>PDB ID:</strong> <a href="${molecule.pdbInfo.url}" target="_blank">${molecule.pdbInfo.id}</a></p>
                `;

                if (molecule.pdbInfo.title) {
                    infoHTML += `<p><strong>标题:</strong> ${molecule.pdbInfo.title}</p>`;
                }

                if (molecule.pdbInfo.authors) {
                    infoHTML += `<p><strong>作者:</strong> ${molecule.pdbInfo.authors}</p>`;
                }
            }

            this.moleculeInfo.innerHTML = infoHTML;
        })
        .catch(error => {
            console.error(`加载PDB分子 ${pdbId} 失败:`, error);
            this.moleculeInfo.innerHTML = `
                <p class="error">加载PDB分子 ${pdbId} 失败</p>
                <p>错误: ${error.message || '未知错误'}</p>
                <p>请检查PDB ID是否正确，或者尝试其他分子。</p>
            `;
        })
        .finally(() => {
            // 恢复加载按钮
            if (this.loadPdbButton) {
                this.loadPdbButton.disabled = false;
                this.loadPdbButton.textContent = '加载';
            }
        });
    }

    /**
     * 处理加载PDB按钮点击
     * @private
     */
    _onLoadPdbClick() {
        const pdbId = this.pdbIdInput.value.trim();

        if (!pdbId) {
            alert('请输入有效的PDB ID');
            return;
        }

        // 验证PDB ID格式 (通常是4个字符)
        if (!/^[a-zA-Z0-9]{4}$/.test(pdbId)) {
            alert('PDB ID应为4个字母或数字的组合');
            return;
        }

        // 加载PDB分子
        this._loadPdbMolecule(pdbId);
    }

    /**
     * 处理分子选择变化
     * @private
     */
    _onMoleculeChange() {
        const moleculeId = this.moleculeSelect.value;
        this._loadMolecule(moleculeId);
    }

    /**
     * 处理显示模式变化
     * @private
     */
    _onDisplayModeChange() {
        const mode = this.displayModeSelect.value;
        this.viewer.setDisplayMode(mode);
    }

    /**
     * 处理动画类型变化
     * @private
     */
    _onAnimationTypeChange() {
        if (!this.animationTypeSelect) return;

        const type = this.animationTypeSelect.value;

        // 根据动画类型设置不同的选项
        let options = {};

        switch (type) {
            case 'vibration':
                options = {
                    amplitude: 0.1,
                    frequency: 0.05
                };
                break;
            case 'rotation':
                options = {
                    axis: [0, 1, 0],
                    speed: 0.5
                };
                break;
            case 'molecularDynamics':
                options = {
                    amplitude: 0.15,
                    speed: 0.8
                };
                break;
            case 'conformationChange':
                options = {
                    speed: 0.3,
                    amplitude: 0.5
                };
                break;
            case 'crystalVibration':
                options = {
                    amplitude: 0.05,
                    frequency: 0.03
                };
                break;
            case 'virusFloat':
                options = {
                    amplitude: 0.3,
                    speed: 0.5
                };
                break;
        }

        // 设置动画类型
        this.viewer.setAnimationType(type, options);

        console.log(`动画类型已更改为: ${type}`);
    }

    /**
     * 处理动画速度变化
     * @private
     */
    _onAnimationSpeedChange() {
        const speed = this.animationSpeedSlider.value / 100;
        this.viewer.setAnimationSpeed(speed);
    }

    /**
     * 处理播放/暂停按钮点击
     * @private
     */
    _onPlayPauseClick() {
        const isPlaying = this.viewer.togglePlayPause();
        this.playPauseButton.textContent = isPlaying ? '暂停' : '播放';
    }

    /**
     * 处理重置按钮点击
     * @private
     */
    _onResetClick() {
        this.viewer.resetView();
    }

    /**
     * 处理渲染质量切换
     * @private
     */
    _onQualityToggle() {
        const highQuality = this.qualityToggle.checked;

        // 更新查看器的渲染质量
        if (this.viewer.useHighQuality !== highQuality) {
            this.viewer.useHighQuality = highQuality;

            // 如果当前有分子，重新加载以应用新的渲染设置
            if (this.viewer.currentMolecule) {
                this.viewer.loadMolecule(this.viewer.currentMolecule);
            }

            console.log(`渲染质量已设置为: ${highQuality ? '高质量' : '标准'}`);
        }
    }

    /**
     * 处理后期处理效果切换
     * @private
     */
    _onEffectsToggle() {
        const useEffects = this.effectsToggle.checked;

        // 更新查看器的后期处理设置
        if (this.viewer.usePostProcessing !== useEffects) {
            this.viewer.usePostProcessing = useEffects;

            // 如果当前有分子，重新初始化后期处理效果
            if (this.viewer.composer) {
                // 重新初始化后期处理
                this.viewer._initPostProcessing();
            }

            console.log(`后期处理效果已${useEffects ? '启用' : '禁用'}`);
        }
    }

    /**
     * 更新播放/暂停按钮状态
     * @param {boolean} isPlaying - 是否正在播放
     */
    updatePlayPauseButton(isPlaying) {
        if (this.playPauseButton) {
            this.playPauseButton.textContent = isPlaying ? '暂停' : '播放';
            this.playPauseButton.classList.toggle('active', isPlaying);
        }
    }

    // ==================== 化学反应控制方法 ====================

    /**
     * 处理反应选择变化
     * @private
     */
    _onReactionSelectChange() {
        const reactionId = this.reactionSelect.value;

        if (!reactionId) {
            this.reactionInfo.innerHTML = '<p>选择一个反应查看详细信息</p>';
            this.startReactionButton.disabled = true;
            return;
        }

        // 初始化反应引擎（如果还没有）
        if (!this.viewer.reactionEngine) {
            this.viewer.reactionEngine = new ReactionEngine(this.viewer);
        }

        // 获取反应信息
        const reactions = this.viewer.reactionEngine.getAvailableReactions();
        const reaction = reactions.find(r => r.id === reactionId);

        if (reaction) {
            this.reactionInfo.innerHTML = `
                <h4>${reaction.name}</h4>
                <p><strong>类型:</strong> ${reaction.type}</p>
                <p><strong>方程式:</strong> ${reaction.equation}</p>
                <p><strong>描述:</strong> ${reaction.description}</p>
            `;
            this.startReactionButton.disabled = false;
        }
    }

    /**
     * 开始反应
     * @private
     */
    _onStartReaction() {
        const reactionId = this.reactionSelect.value;
        if (!reactionId || !this.viewer.reactionEngine) return;

        const speed = parseFloat(this.reactionSpeedSlider.value);
        const success = this.viewer.reactionEngine.startReaction(reactionId, { speed });

        if (success) {
            this.startReactionButton.disabled = true;
            this.stopReactionButton.disabled = false;
            this.reactionSelect.disabled = true;

            // 开始进度更新
            this._startProgressUpdate();
        }
    }

    /**
     * 停止反应
     * @private
     */
    _onStopReaction() {
        if (this.viewer.reactionEngine) {
            this.viewer.reactionEngine.stopReaction();
        }

        this._resetReactionControls();
    }

    /**
     * 重置反应
     * @private
     */
    _onResetReaction() {
        if (this.viewer.reactionEngine) {
            this.viewer.reactionEngine.stopReaction();
        }

        this._resetReactionControls();
        this.reactionProgressFill.style.width = '0%';
        this.reactionProgressText.textContent = '0%';

        // 清除查看器
        this.viewer.clearMolecule();
    }

    /**
     * 处理反应速度变化
     * @private
     */
    _onReactionSpeedChange() {
        const speed = parseFloat(this.reactionSpeedSlider.value);
        this.reactionSpeedValue.textContent = speed.toFixed(1) + 'x';

        // 如果反应正在进行，更新速度
        if (this.viewer.reactionEngine && this.viewer.reactionEngine.isReacting) {
            this.viewer.reactionEngine.reactionSpeed = speed;
        }
    }

    /**
     * 重置反应控制状态
     * @private
     */
    _resetReactionControls() {
        this.startReactionButton.disabled = false;
        this.stopReactionButton.disabled = true;
        this.reactionSelect.disabled = false;

        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
            this.progressUpdateInterval = null;
        }
    }

    /**
     * 开始进度更新
     * @private
     */
    _startProgressUpdate() {
        this.progressUpdateInterval = setInterval(() => {
            if (!this.viewer.reactionEngine || !this.viewer.reactionEngine.isReacting) {
                this._resetReactionControls();
                return;
            }

            const progress = this.viewer.reactionEngine.reactionProgress;
            const percentage = Math.round(progress * 100);

            this.reactionProgressFill.style.width = percentage + '%';
            this.reactionProgressText.textContent = percentage + '%';

            if (progress >= 1.0) {
                setTimeout(() => {
                    this._resetReactionControls();
                }, 1000);
            }
        }, 100);
    }
}
