/**
 * 分子数据库
 * 包含各种分子的原子坐标和键连接信息
 */

// 全局分子数据对象
const MOLECULES = {


    // 水分子 H2O
    water: {
        name: '水分子',
        category: '小分子',
        atoms: [
            { symbol: 'O', position: [0, 0, 0] },
            { symbol: 'H', position: [0.8, 0.6, 0] },
            { symbol: 'H', position: [-0.8, 0.6, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [0, 2], order: 1 }
        ]
    },

    // 甲烷 CH4
    methane: {
        name: '甲烷',
        category: '小分子',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'H', position: [0.63, 0.63, 0.63] },
            { symbol: 'H', position: [-0.63, -0.63, 0.63] },
            { symbol: 'H', position: [0.63, -0.63, -0.63] },
            { symbol: 'H', position: [-0.63, 0.63, -0.63] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [0, 2], order: 1 },
            { atoms: [0, 3], order: 1 },
            { atoms: [0, 4], order: 1 }
        ]
    },

    // 色氨酸 - 氨基酸
    tryptophan: {
        name: '色氨酸',
        category: '氨基酸',
        atoms: [
            // 主链
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] }, // α-碳
            { symbol: 'C', position: [2.3, -0.6, 0] }, // 羧基碳
            { symbol: 'O', position: [2.0, -1.8, 0] }, // 羧基氧
            { symbol: 'O', position: [3.5, -0.3, 0] }, // 羧基氧
            { symbol: 'H', position: [-0.3, 0.9, 0] }, // 氨基氢
            { symbol: 'H', position: [-0.6, -0.7, 0] }, // 氨基氢

            // 侧链 - 吲哚环
            { symbol: 'C', position: [1.5, 1.5, 1.1] }, // β-碳
            { symbol: 'C', position: [2.8, 2.2, 1.3] }, // 连接吲哚环的碳
            { symbol: 'C', position: [3.0, 3.2, 2.2] }, // 吲哚环碳1
            { symbol: 'C', position: [4.2, 3.8, 2.3] }, // 吲哚环碳2
            { symbol: 'C', position: [5.2, 3.4, 1.5] }, // 吲哚环碳3
            { symbol: 'C', position: [5.0, 2.4, 0.6] }, // 吲哚环碳4
            { symbol: 'C', position: [3.8, 1.8, 0.5] }, // 吲哚环碳5
            { symbol: 'N', position: [3.9, 3.8, 3.2] }, // 吲哚环氮
            { symbol: 'C', position: [2.8, 3.0, 3.3] }, // 吲哚环碳6

            // 氢原子
            { symbol: 'H', position: [1.3, 1.0, 0] }, // α-碳氢
            { symbol: 'H', position: [0.7, 2.2, 1.3] }, // β-碳氢
            { symbol: 'H', position: [1.3, 0.9, 2.0] }, // β-碳氢
            { symbol: 'H', position: [4.5, 4.6, 3.6] }, // 吲哚环氮氢
            { symbol: 'H', position: [6.2, 3.9, 1.6] }, // 吲哚环氢
            { symbol: 'H', position: [5.8, 2.1, 0.0] }, // 吲哚环氢
            { symbol: 'H', position: [3.6, 1.0, -0.2] }, // 吲哚环氢
            { symbol: 'H', position: [2.5, 3.0, 4.3] }  // 吲哚环氢
        ],
        bonds: [
            // 主链键
            { atoms: [0, 1], order: 1 }, // N-Cα
            { atoms: [1, 2], order: 1 }, // Cα-C
            { atoms: [2, 3], order: 2 }, // C=O
            { atoms: [2, 4], order: 1 }, // C-O
            { atoms: [0, 5], order: 1 }, // N-H
            { atoms: [0, 6], order: 1 }, // N-H

            // 侧链键
            { atoms: [1, 7], order: 1 }, // Cα-Cβ
            { atoms: [7, 8], order: 1 }, // Cβ-C
            { atoms: [8, 9], order: 1 }, // 吲哚环
            { atoms: [9, 10], order: 2 },
            { atoms: [10, 11], order: 1 },
            { atoms: [11, 12], order: 2 },
            { atoms: [12, 13], order: 1 },
            { atoms: [8, 13], order: 2 },
            { atoms: [10, 14], order: 1 },
            { atoms: [14, 15], order: 1 },
            { atoms: [9, 15], order: 2 },

            // 氢键
            { atoms: [1, 16], order: 1 },
            { atoms: [7, 17], order: 1 },
            { atoms: [7, 18], order: 1 },
            { atoms: [14, 19], order: 1 },
            { atoms: [11, 20], order: 1 },
            { atoms: [12, 21], order: 1 },
            { atoms: [13, 22], order: 1 },
            { atoms: [15, 23], order: 1 }
        ]
    },

    // 组氨酸 - 氨基酸
    histidine: {
        name: '组氨酸',
        category: '氨基酸',
        atoms: [
            // 主链
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] }, // α-碳
            { symbol: 'C', position: [2.3, -0.6, 0] }, // 羧基碳
            { symbol: 'O', position: [2.0, -1.8, 0] }, // 羧基氧
            { symbol: 'O', position: [3.5, -0.3, 0] }, // 羧基氧
            { symbol: 'H', position: [-0.3, 0.9, 0] }, // 氨基氢
            { symbol: 'H', position: [-0.6, -0.7, 0] }, // 氨基氢

            // 侧链 - 咪唑环
            { symbol: 'C', position: [1.5, 1.5, 1.1] }, // β-碳
            { symbol: 'C', position: [2.8, 2.2, 1.3] }, // 连接咪唑环的碳
            { symbol: 'N', position: [3.0, 3.2, 2.2] }, // 咪唑环氮1
            { symbol: 'C', position: [4.2, 3.5, 2.3] }, // 咪唑环碳1
            { symbol: 'N', position: [4.8, 2.7, 1.4] }, // 咪唑环氮2
            { symbol: 'C', position: [3.9, 1.9, 0.8] }, // 咪唑环碳2

            // 氢原子
            { symbol: 'H', position: [1.3, 1.0, 0] }, // α-碳氢
            { symbol: 'H', position: [0.7, 2.2, 1.3] }, // β-碳氢
            { symbol: 'H', position: [1.3, 0.9, 2.0] }, // β-碳氢
            { symbol: 'H', position: [2.3, 3.7, 2.7] }, // 咪唑环氮氢
            { symbol: 'H', position: [4.7, 4.3, 2.9] }, // 咪唑环碳氢
            { symbol: 'H', position: [4.1, 1.2, 0.0] }  // 咪唑环碳氢
        ],
        bonds: [
            // 主链键
            { atoms: [0, 1], order: 1 }, // N-Cα
            { atoms: [1, 2], order: 1 }, // Cα-C
            { atoms: [2, 3], order: 2 }, // C=O
            { atoms: [2, 4], order: 1 }, // C-O
            { atoms: [0, 5], order: 1 }, // N-H
            { atoms: [0, 6], order: 1 }, // N-H

            // 侧链键
            { atoms: [1, 7], order: 1 }, // Cα-Cβ
            { atoms: [7, 8], order: 1 }, // Cβ-C
            { atoms: [8, 9], order: 1 }, // 咪唑环
            { atoms: [9, 10], order: 1 },
            { atoms: [10, 11], order: 2 },
            { atoms: [11, 12], order: 1 },
            { atoms: [8, 12], order: 2 },

            // 氢键
            { atoms: [1, 13], order: 1 },
            { atoms: [7, 14], order: 1 },
            { atoms: [7, 15], order: 1 },
            { atoms: [9, 16], order: 1 },
            { atoms: [10, 17], order: 1 },
            { atoms: [12, 18], order: 1 }
        ]
    },

    // 腺嘌呤 - 核苷酸
    adenine: {
        name: '腺嘌呤',
        category: '核苷酸',
        atoms: [
            // 嘌呤环
            { symbol: 'N', position: [0, 0, 0] },      // N1
            { symbol: 'C', position: [1.1, 0.7, 0] },  // C2
            { symbol: 'N', position: [2.3, 0.1, 0] },  // N3
            { symbol: 'C', position: [2.4, -1.2, 0] }, // C4
            { symbol: 'C', position: [1.3, -2.0, 0] }, // C5
            { symbol: 'C', position: [0.1, -1.4, 0] }, // C6
            { symbol: 'N', position: [3.7, -1.7, 0] }, // N7
            { symbol: 'C', position: [3.6, -3.1, 0] }, // C8
            { symbol: 'N', position: [2.3, -3.4, 0] }, // N9
            { symbol: 'N', position: [-1.1, -2.0, 0] },// N10 (氨基)

            // 氢原子
            { symbol: 'H', position: [1.0, 1.8, 0] },  // H2
            { symbol: 'H', position: [4.5, -3.7, 0] }, // H8
            { symbol: 'H', position: [2.0, -4.4, 0] }, // H9
            { symbol: 'H', position: [-1.9, -1.4, 0] },// H10a
            { symbol: 'H', position: [-1.2, -3.0, 0] } // H10b
        ],
        bonds: [
            // 嘌呤环键
            { atoms: [0, 1], order: 1 },  // N1-C2
            { atoms: [1, 2], order: 2 },  // C2=N3
            { atoms: [2, 3], order: 1 },  // N3-C4
            { atoms: [3, 4], order: 1 },  // C4-C5
            { atoms: [4, 5], order: 2 },  // C5=C6
            { atoms: [5, 0], order: 1 },  // C6-N1
            { atoms: [3, 6], order: 2 },  // C4=N7
            { atoms: [6, 7], order: 1 },  // N7-C8
            { atoms: [7, 8], order: 2 },  // C8=N9
            { atoms: [8, 4], order: 1 },  // N9-C5
            { atoms: [5, 9], order: 1 },  // C6-N10

            // 氢键
            { atoms: [1, 10], order: 1 }, // C2-H2
            { atoms: [7, 11], order: 1 }, // C8-H8
            { atoms: [8, 12], order: 1 }, // N9-H9
            { atoms: [9, 13], order: 1 }, // N10-H10a
            { atoms: [9, 14], order: 1 }  // N10-H10b
        ]
    },

    // 胸腺嘧啶 - 核苷酸
    thymine: {
        name: '胸腺嘧啶',
        category: '核苷酸',
        atoms: [
            // 嘧啶环
            { symbol: 'N', position: [0, 0, 0] },      // N1
            { symbol: 'C', position: [1.1, 0.7, 0] },  // C2
            { symbol: 'N', position: [2.3, 0.1, 0] },  // N3
            { symbol: 'C', position: [2.4, -1.2, 0] }, // C4
            { symbol: 'C', position: [1.3, -2.0, 0] }, // C5
            { symbol: 'C', position: [0.1, -1.4, 0] }, // C6
            { symbol: 'O', position: [1.0, 2.0, 0] },  // O7 (C2上的氧)
            { symbol: 'O', position: [3.6, -1.7, 0] }, // O8 (C4上的氧)
            { symbol: 'C', position: [1.4, -3.5, 0] }, // C9 (C5上的甲基)

            // 氢原子
            { symbol: 'H', position: [-0.9, 0.4, 0] }, // H1
            { symbol: 'H', position: [3.1, 0.7, 0] },  // H3
            { symbol: 'H', position: [-0.8, -2.0, 0] },// H6
            { symbol: 'H', position: [0.4, -3.9, 0] }, // H9a
            { symbol: 'H', position: [2.0, -3.9, 0.8] },// H9b
            { symbol: 'H', position: [1.9, -3.9, -0.8] }// H9c
        ],
        bonds: [
            // 嘧啶环键
            { atoms: [0, 1], order: 1 },  // N1-C2
            { atoms: [1, 2], order: 1 },  // C2-N3
            { atoms: [2, 3], order: 1 },  // N3-C4
            { atoms: [3, 4], order: 1 },  // C4-C5
            { atoms: [4, 5], order: 2 },  // C5=C6
            { atoms: [5, 0], order: 1 },  // C6-N1
            { atoms: [1, 6], order: 2 },  // C2=O7
            { atoms: [3, 7], order: 2 },  // C4=O8
            { atoms: [4, 8], order: 1 },  // C5-C9

            // 氢键
            { atoms: [0, 9], order: 1 },  // N1-H1
            { atoms: [2, 10], order: 1 }, // N3-H3
            { atoms: [5, 11], order: 1 }, // C6-H6
            { atoms: [8, 12], order: 1 }, // C9-H9a
            { atoms: [8, 13], order: 1 }, // C9-H9b
            { atoms: [8, 14], order: 1 }  // C9-H9c
        ]
    },

    // 多巴胺 - 神经递质
    dopamine: {
        name: '多巴胺',
        category: '神经递质',
        atoms: [
            // 苯环
            { symbol: 'C', position: [0, 0, 0] },      // C1
            { symbol: 'C', position: [1.4, 0, 0] },    // C2
            { symbol: 'C', position: [2.1, 1.2, 0] },  // C3
            { symbol: 'C', position: [1.4, 2.4, 0] },  // C4
            { symbol: 'C', position: [0, 2.4, 0] },    // C5
            { symbol: 'C', position: [-0.7, 1.2, 0] }, // C6

            // 羟基
            { symbol: 'O', position: [2.8, 3.6, 0] },  // O7 (C3上的羟基)
            { symbol: 'O', position: [2.1, 3.6, 0] },  // O8 (C4上的羟基)

            // 乙胺侧链
            { symbol: 'C', position: [-1.4, -1.2, 0] },// C9 (C1上的亚甲基)
            { symbol: 'C', position: [-2.8, -1.2, 0] },// C10 (C9上的亚甲基)
            { symbol: 'N', position: [-3.5, -2.4, 0] },// N11 (C10上的氨基)

            // 氢原子
            { symbol: 'H', position: [1.9, -0.9, 0] }, // H2
            { symbol: 'H', position: [3.2, 1.2, 0] },  // H3
            { symbol: 'H', position: [-0.5, 3.3, 0] }, // H5
            { symbol: 'H', position: [-1.8, 1.2, 0] }, // H6
            { symbol: 'H', position: [3.3, 4.4, 0] },  // H7
            { symbol: 'H', position: [1.6, 4.4, 0] },  // H8
            { symbol: 'H', position: [-1.1, -2.2, 0] },// H9a
            { symbol: 'H', position: [-3.1, -0.2, 0] },// H10a
            { symbol: 'H', position: [-3.1, -3.3, 0] },// H11a
            { symbol: 'H', position: [-4.5, -2.4, 0] } // H11b
        ],
        bonds: [
            // 苯环键
            { atoms: [0, 1], order: 1 },  // C1-C2
            { atoms: [1, 2], order: 2 },  // C2=C3
            { atoms: [2, 3], order: 1 },  // C3-C4
            { atoms: [3, 4], order: 2 },  // C4=C5
            { atoms: [4, 5], order: 1 },  // C5-C6
            { atoms: [5, 0], order: 2 },  // C6=C1

            // 羟基键
            { atoms: [2, 6], order: 1 },  // C3-O7
            { atoms: [3, 7], order: 1 },  // C4-O8

            // 侧链键
            { atoms: [0, 8], order: 1 },  // C1-C9
            { atoms: [8, 9], order: 1 },  // C9-C10
            { atoms: [9, 10], order: 1 }, // C10-N11

            // 氢键
            { atoms: [1, 11], order: 1 }, // C2-H2
            { atoms: [2, 12], order: 1 }, // C3-H3
            { atoms: [4, 13], order: 1 }, // C5-H5
            { atoms: [5, 14], order: 1 }, // C6-H6
            { atoms: [6, 15], order: 1 }, // O7-H7
            { atoms: [7, 16], order: 1 }, // O8-H8
            { atoms: [8, 17], order: 1 }, // C9-H9a
            { atoms: [9, 18], order: 1 }, // C10-H10a
            { atoms: [10, 19], order: 1 },// N11-H11a
            { atoms: [10, 20], order: 1 } // N11-H11b
        ]
    },

    // 青霉素G - 抗生素
    penicillinG: {
        name: '青霉素G',
        category: '抗生素',
        atoms: [
            // β-内酰胺环和噻唑环
            { symbol: 'C', position: [0, 0, 0] },      // C1
            { symbol: 'C', position: [1.4, 0.4, 0] },  // C2
            { symbol: 'N', position: [1.6, 1.8, 0] },  // N3
            { symbol: 'C', position: [0.4, 2.4, 0] },  // C4
            { symbol: 'C', position: [-0.7, 1.4, 0] }, // C5
            { symbol: 'S', position: [-0.3, -1.5, 0] },// S6
            { symbol: 'C', position: [-1.8, -0.7, 0] },// C7
            { symbol: 'C', position: [-2.0, 0.7, 0] }, // C8

            // 羧基
            { symbol: 'C', position: [2.5, -0.5, 0] }, // C9
            { symbol: 'O', position: [2.3, -1.7, 0] }, // O10
            { symbol: 'O', position: [3.7, -0.1, 0] }, // O11

            // 苯基乙酰基侧链
            { symbol: 'C', position: [0.4, 3.9, 0] },  // C12
            { symbol: 'O', position: [1.5, 4.5, 0] },  // O13
            { symbol: 'N', position: [-0.8, 4.5, 0] }, // N14
            { symbol: 'C', position: [-0.8, 5.9, 0] }, // C15
            { symbol: 'C', position: [-2.2, 6.5, 0] }, // C16

            // 苯环
            { symbol: 'C', position: [-2.2, 8.0, 0] }, // C17
            { symbol: 'C', position: [-3.4, 8.7, 0] }, // C18
            { symbol: 'C', position: [-4.6, 8.0, 0] }, // C19
            { symbol: 'C', position: [-4.6, 6.6, 0] }, // C20
            { symbol: 'C', position: [-3.4, 5.9, 0] }, // C21

            // 甲基
            { symbol: 'C', position: [-2.0, 1.9, 0] }, // C22
            { symbol: 'C', position: [-3.0, -1.5, 0] } // C23
        ],
        bonds: [
            // β-内酰胺环和噻唑环键
            { atoms: [0, 1], order: 1 },  // C1-C2
            { atoms: [1, 2], order: 1 },  // C2-N3
            { atoms: [2, 3], order: 1 },  // N3-C4
            { atoms: [3, 4], order: 1 },  // C4-C5
            { atoms: [4, 0], order: 1 },  // C5-C1
            { atoms: [0, 5], order: 1 },  // C1-S6
            { atoms: [5, 6], order: 1 },  // S6-C7
            { atoms: [6, 7], order: 2 },  // C7=C8
            { atoms: [7, 4], order: 1 },  // C8-C5

            // 羧基键
            { atoms: [1, 8], order: 1 },  // C2-C9
            { atoms: [8, 9], order: 2 },  // C9=O10
            { atoms: [8, 10], order: 1 }, // C9-O11

            // 苯基乙酰基侧链键
            { atoms: [3, 11], order: 1 }, // C4-C12
            { atoms: [11, 12], order: 2 },// C12=O13
            { atoms: [11, 13], order: 1 },// C12-N14
            { atoms: [13, 14], order: 1 },// N14-C15
            { atoms: [14, 15], order: 1 },// C15-C16

            // 苯环键
            { atoms: [15, 16], order: 1 },// C16-C17
            { atoms: [16, 17], order: 2 },// C17=C18
            { atoms: [17, 18], order: 1 },// C18-C19
            { atoms: [18, 19], order: 2 },// C19=C20
            { atoms: [19, 20], order: 1 },// C20-C21
            { atoms: [20, 15], order: 2 },// C21=C16

            // 甲基键
            { atoms: [7, 21], order: 1 }, // C8-C22
            { atoms: [6, 22], order: 1 }  // C7-C23
        ]
    },

    // ==================== 无机分子 ====================

    // 氨气 NH3
    ammonia: {
        name: '氨气',
        category: '无机分子',
        formula: 'NH₃',
        description: '氨气是一种重要的工业化学品，具有刺激性气味，是制造化肥的重要原料。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'H', position: [0.8, 0.6, 0] },
            { symbol: 'H', position: [-0.8, 0.6, 0] },
            { symbol: 'H', position: [0, -0.9, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [0, 2], order: 1 },
            { atoms: [0, 3], order: 1 }
        ]
    },

    // 硫化氢 H2S
    hydrogenSulfide: {
        name: '硫化氢',
        category: '无机分子',
        formula: 'H₂S',
        description: '硫化氢是一种有毒气体，具有臭鸡蛋味，在地质过程中自然产生。',
        atoms: [
            { symbol: 'S', position: [0, 0, 0] },
            { symbol: 'H', position: [0.9, 0.7, 0] },
            { symbol: 'H', position: [-0.9, 0.7, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [0, 2], order: 1 }
        ]
    },

    // 氯化氢 HCl
    hydrogenChloride: {
        name: '氯化氢',
        category: '无机分子',
        formula: 'HCl',
        description: '氯化氢是一种强酸性气体，溶于水形成盐酸。',
        atoms: [
            { symbol: 'H', position: [0, 0, 0] },
            { symbol: 'Cl', position: [1.3, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 }
        ]
    },

    // 一氧化碳 CO
    carbonMonoxide: {
        name: '一氧化碳',
        category: '无机分子',
        formula: 'CO',
        description: '一氧化碳是一种无色无味的有毒气体，燃烧不完全时产生。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'O', position: [1.1, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 3 }
        ]
    },

    // 二氧化碳 CO2
    carbonDioxide: {
        name: '二氧化碳',
        category: '无机分子',
        formula: 'CO₂',
        description: '二氧化碳是一种重要的温室气体，由一个碳原子和两个氧原子组成，呈线性结构。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'O', position: [1.2, 0, 0] },
            { symbol: 'O', position: [-1.2, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 2 },
            { atoms: [0, 2], order: 2 }
        ]
    },

    // 一氧化氮 NO
    nitricOxide: {
        name: '一氧化氮',
        category: '无机分子',
        formula: 'NO',
        description: '一氧化氮是重要的信号分子，在血管舒张和神经传递中起关键作用。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'O', position: [1.15, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 2 }
        ]
    },

    // 二氧化氮 NO2
    nitrogenDioxide: {
        name: '二氧化氮',
        category: '无机分子',
        formula: 'NO₂',
        description: '二氧化氮是一种红棕色气体，是大气污染物之一。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'O', position: [1.0, 0.8, 0] },
            { symbol: 'O', position: [1.0, -0.8, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 2 },
            { atoms: [0, 2], order: 1 }
        ]
    },

    // 二氧化硫 SO2
    sulfurDioxide: {
        name: '二氧化硫',
        category: '无机分子',
        formula: 'SO₂',
        description: '二氧化硫是一种有刺激性气味的气体，是酸雨的主要成因之一。',
        atoms: [
            { symbol: 'S', position: [0, 0, 0] },
            { symbol: 'O', position: [1.1, 0.9, 0] },
            { symbol: 'O', position: [1.1, -0.9, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 2 },
            { atoms: [0, 2], order: 2 }
        ]
    },

    // ==================== 有机小分子 ====================

    // 乙烷 C2H6
    ethane: {
        name: '乙烷',
        category: '有机小分子',
        formula: 'C₂H₆',
        description: '乙烷是最简单的烷烃之一，是天然气的主要成分，常用作燃料。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.5, 0, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [-0.5, 0, 0.9] },
            { symbol: 'H', position: [2.0, 0.9, 0] },
            { symbol: 'H', position: [2.0, -0.9, 0] },
            { symbol: 'H', position: [2.0, 0, 0.9] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [0, 2], order: 1 },
            { atoms: [0, 3], order: 1 },
            { atoms: [0, 4], order: 1 },
            { atoms: [1, 5], order: 1 },
            { atoms: [1, 6], order: 1 },
            { atoms: [1, 7], order: 1 }
        ]
    },

    // 乙烯 C2H4
    ethylene: {
        name: '乙烯',
        category: '有机小分子',
        formula: 'C₂H₄',
        description: '乙烯是最简单的烯烃，是重要的化工原料，用于制造塑料。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.3, 0, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [1.8, 0.9, 0] },
            { symbol: 'H', position: [1.8, -0.9, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 2 },
            { atoms: [0, 2], order: 1 },
            { atoms: [0, 3], order: 1 },
            { atoms: [1, 4], order: 1 },
            { atoms: [1, 5], order: 1 }
        ]
    },

    // 乙炔 C2H2
    acetylene: {
        name: '乙炔',
        category: '有机小分子',
        formula: 'C₂H₂',
        description: '乙炔是最简单的炔烃，燃烧时产生高温，常用于焊接。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.2, 0, 0] },
            { symbol: 'H', position: [-1.0, 0, 0] },
            { symbol: 'H', position: [2.2, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 3 },
            { atoms: [0, 2], order: 1 },
            { atoms: [1, 3], order: 1 }
        ]
    },

    // 苯 C6H6
    benzene: {
        name: '苯',
        category: '有机小分子',
        formula: 'C₆H₆',
        description: '苯是最简单的芳香烃，具有特殊的芳香性，是重要的化工原料。',
        atoms: [
            { symbol: 'C', position: [1.0, 0, 0] },
            { symbol: 'C', position: [0.5, 0.87, 0] },
            { symbol: 'C', position: [-0.5, 0.87, 0] },
            { symbol: 'C', position: [-1.0, 0, 0] },
            { symbol: 'C', position: [-0.5, -0.87, 0] },
            { symbol: 'C', position: [0.5, -0.87, 0] },
            { symbol: 'H', position: [1.8, 0, 0] },
            { symbol: 'H', position: [0.9, 1.56, 0] },
            { symbol: 'H', position: [-0.9, 1.56, 0] },
            { symbol: 'H', position: [-1.8, 0, 0] },
            { symbol: 'H', position: [-0.9, -1.56, 0] },
            { symbol: 'H', position: [0.9, -1.56, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 2 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 2 },
            { atoms: [0, 6], order: 1 },
            { atoms: [1, 7], order: 1 },
            { atoms: [2, 8], order: 1 },
            { atoms: [3, 9], order: 1 },
            { atoms: [4, 10], order: 1 },
            { atoms: [5, 11], order: 1 }
        ]
    },

    // 乙醇 C2H6O
    ethanol: {
        name: '乙醇',
        category: '有机小分子',
        formula: 'C₂H₆O',
        description: '乙醇是酒精的主要成分，也是重要的溶剂和燃料。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.5, 0, 0] },
            { symbol: 'O', position: [2.2, 1.2, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [-0.5, 0, 0.9] },
            { symbol: 'H', position: [1.8, -0.5, 0.9] },
            { symbol: 'H', position: [1.8, -0.5, -0.9] },
            { symbol: 'H', position: [3.1, 1.0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [0, 3], order: 1 },
            { atoms: [0, 4], order: 1 },
            { atoms: [0, 5], order: 1 },
            { atoms: [1, 6], order: 1 },
            { atoms: [1, 7], order: 1 },
            { atoms: [2, 8], order: 1 }
        ]
    },

    // 甲醇 CH4O
    methanol: {
        name: '甲醇',
        category: '有机小分子',
        formula: 'CH₄O',
        description: '甲醇是最简单的醇类，有毒，常用作溶剂和燃料。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'O', position: [1.4, 0, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [-0.5, 0, 0.9] },
            { symbol: 'H', position: [1.8, 0.8, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [0, 2], order: 1 },
            { atoms: [0, 3], order: 1 },
            { atoms: [0, 4], order: 1 },
            { atoms: [1, 5], order: 1 }
        ]
    },

    // 丙酮 C3H6O
    acetone: {
        name: '丙酮',
        category: '有机小分子',
        formula: 'C₃H₆O',
        description: '丙酮是常用的有机溶剂，也是指甲油去除剂的主要成分。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.5, 0, 0] },
            { symbol: 'C', position: [3.0, 0, 0] },
            { symbol: 'O', position: [1.5, 1.2, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [-0.5, 0, 0.9] },
            { symbol: 'H', position: [3.5, 0.9, 0] },
            { symbol: 'H', position: [3.5, -0.9, 0] },
            { symbol: 'H', position: [3.5, 0, 0.9] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [1, 3], order: 2 },
            { atoms: [0, 4], order: 1 },
            { atoms: [0, 5], order: 1 },
            { atoms: [0, 6], order: 1 },
            { atoms: [2, 7], order: 1 },
            { atoms: [2, 8], order: 1 },
            { atoms: [2, 9], order: 1 }
        ]
    },

    // ==================== 更多氨基酸 ====================

    // 甘氨酸 - 最简单的氨基酸
    glycine: {
        name: '甘氨酸',
        category: '氨基酸',
        formula: 'C₂H₅NO₂',
        description: '甘氨酸是最简单的氨基酸，没有侧链，是蛋白质的重要组成部分。',
        atoms: [
            // 主链
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] }, // α-碳
            { symbol: 'C', position: [2.3, -0.6, 0] }, // 羧基碳
            { symbol: 'O', position: [2.0, -1.8, 0] }, // 羧基氧
            { symbol: 'O', position: [3.5, -0.3, 0] }, // 羧基氧
            { symbol: 'H', position: [-0.3, 0.9, 0] }, // 氨基氢
            { symbol: 'H', position: [-0.6, -0.7, 0] }, // 氨基氢
            { symbol: 'H', position: [1.3, 1.0, 0] }, // α-碳氢
            { symbol: 'H', position: [1.0, 1.2, 0.8] } // α-碳氢
        ],
        bonds: [
            { atoms: [0, 1], order: 1 }, // N-Cα
            { atoms: [1, 2], order: 1 }, // Cα-C
            { atoms: [2, 3], order: 2 }, // C=O
            { atoms: [2, 4], order: 1 }, // C-O
            { atoms: [0, 5], order: 1 }, // N-H
            { atoms: [0, 6], order: 1 }, // N-H
            { atoms: [1, 7], order: 1 }, // Cα-H
            { atoms: [1, 8], order: 1 }  // Cα-H
        ]
    },

    // 丙氨酸
    alanine: {
        name: '丙氨酸',
        category: '氨基酸',
        formula: 'C₃H₇NO₂',
        description: '丙氨酸是一种简单的氨基酸，侧链为甲基，是蛋白质的重要组成部分。',
        atoms: [
            // 主链
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] }, // α-碳
            { symbol: 'C', position: [2.3, -0.6, 0] }, // 羧基碳
            { symbol: 'O', position: [2.0, -1.8, 0] }, // 羧基氧
            { symbol: 'O', position: [3.5, -0.3, 0] }, // 羧基氧
            { symbol: 'H', position: [-0.3, 0.9, 0] }, // 氨基氢
            { symbol: 'H', position: [-0.6, -0.7, 0] }, // 氨基氢

            // 侧链 - 甲基
            { symbol: 'C', position: [1.5, 1.5, 1.1] }, // β-碳

            // 氢原子
            { symbol: 'H', position: [1.3, 1.0, 0] }, // α-碳氢
            { symbol: 'H', position: [0.7, 2.2, 1.3] }, // β-碳氢
            { symbol: 'H', position: [1.3, 0.9, 2.0] }, // β-碳氢
            { symbol: 'H', position: [2.5, 1.8, 1.2] }  // β-碳氢
        ],
        bonds: [
            // 主链键
            { atoms: [0, 1], order: 1 }, // N-Cα
            { atoms: [1, 2], order: 1 }, // Cα-C
            { atoms: [2, 3], order: 2 }, // C=O
            { atoms: [2, 4], order: 1 }, // C-O
            { atoms: [0, 5], order: 1 }, // N-H
            { atoms: [0, 6], order: 1 }, // N-H

            // 侧链键
            { atoms: [1, 7], order: 1 }, // Cα-Cβ

            // 氢键
            { atoms: [1, 8], order: 1 },
            { atoms: [7, 9], order: 1 },
            { atoms: [7, 10], order: 1 },
            { atoms: [7, 11], order: 1 }
        ]
    },

    // 缬氨酸
    valine: {
        name: '缬氨酸',
        category: '氨基酸',
        formula: 'C₅H₁₁NO₂',
        description: '缬氨酸是一种支链氨基酸，是人体必需氨基酸之一。',
        atoms: [
            // 主链
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] }, // α-碳
            { symbol: 'C', position: [2.3, -0.6, 0] }, // 羧基碳
            { symbol: 'O', position: [2.0, -1.8, 0] }, // 羧基氧
            { symbol: 'O', position: [3.5, -0.3, 0] }, // 羧基氧
            { symbol: 'H', position: [-0.3, 0.9, 0] }, // 氨基氢
            { symbol: 'H', position: [-0.6, -0.7, 0] }, // 氨基氢

            // 侧链 - 异丙基
            { symbol: 'C', position: [1.5, 1.5, 1.1] }, // β-碳
            { symbol: 'C', position: [0.5, 2.5, 1.8] }, // γ-碳1
            { symbol: 'C', position: [2.8, 2.2, 1.3] }, // γ-碳2

            // 氢原子
            { symbol: 'H', position: [1.3, 1.0, 0] }, // α-碳氢
            { symbol: 'H', position: [1.3, 0.9, 2.0] }, // β-碳氢
            { symbol: 'H', position: [0.7, 3.5, 2.0] }, // γ-碳1氢
            { symbol: 'H', position: [-0.5, 2.3, 1.5] }, // γ-碳1氢
            { symbol: 'H', position: [0.3, 2.2, 2.8] }, // γ-碳1氢
            { symbol: 'H', position: [3.5, 1.5, 1.8] }, // γ-碳2氢
            { symbol: 'H', position: [3.2, 3.0, 0.7] }, // γ-碳2氢
            { symbol: 'H', position: [2.9, 2.5, 2.3] }  // γ-碳2氢
        ],
        bonds: [
            // 主链键
            { atoms: [0, 1], order: 1 }, // N-Cα
            { atoms: [1, 2], order: 1 }, // Cα-C
            { atoms: [2, 3], order: 2 }, // C=O
            { atoms: [2, 4], order: 1 }, // C-O
            { atoms: [0, 5], order: 1 }, // N-H
            { atoms: [0, 6], order: 1 }, // N-H

            // 侧链键
            { atoms: [1, 7], order: 1 }, // Cα-Cβ
            { atoms: [7, 8], order: 1 }, // Cβ-Cγ1
            { atoms: [7, 9], order: 1 }, // Cβ-Cγ2

            // 氢键
            { atoms: [1, 10], order: 1 },
            { atoms: [7, 11], order: 1 },
            { atoms: [8, 12], order: 1 },
            { atoms: [8, 13], order: 1 },
            { atoms: [8, 14], order: 1 },
            { atoms: [9, 15], order: 1 },
            { atoms: [9, 16], order: 1 },
            { atoms: [9, 17], order: 1 }
        ]
    },

    // 苯丙氨酸
    phenylalanine: {
        name: '苯丙氨酸',
        category: '氨基酸',
        formula: 'C₉H₁₁NO₂',
        description: '苯丙氨酸是一种芳香族氨基酸，是人体必需氨基酸之一，是酪氨酸的前体。',
        atoms: [
            // 主链
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] }, // α-碳
            { symbol: 'C', position: [2.3, -0.6, 0] }, // 羧基碳
            { symbol: 'O', position: [2.0, -1.8, 0] }, // 羧基氧
            { symbol: 'O', position: [3.5, -0.3, 0] }, // 羧基氧
            { symbol: 'H', position: [-0.3, 0.9, 0] }, // 氨基氢
            { symbol: 'H', position: [-0.6, -0.7, 0] }, // 氨基氢

            // 侧链 - 苄基
            { symbol: 'C', position: [1.5, 1.5, 1.1] }, // β-碳
            { symbol: 'C', position: [2.8, 2.2, 1.3] }, // 连接苯环的碳

            // 苯环
            { symbol: 'C', position: [2.8, 3.7, 1.3] }, // 苯环碳1
            { symbol: 'C', position: [4.0, 4.4, 1.5] }, // 苯环碳2
            { symbol: 'C', position: [5.2, 3.7, 1.7] }, // 苯环碳3
            { symbol: 'C', position: [5.2, 2.2, 1.7] }, // 苯环碳4
            { symbol: 'C', position: [4.0, 1.5, 1.5] }, // 苯环碳5

            // 氢原子
            { symbol: 'H', position: [1.3, 1.0, 0] }, // α-碳氢
            { symbol: 'H', position: [0.7, 2.2, 1.3] }, // β-碳氢
            { symbol: 'H', position: [1.3, 0.9, 2.0] }, // β-碳氢
            { symbol: 'H', position: [1.9, 4.2, 1.1] }, // 苯环氢
            { symbol: 'H', position: [4.0, 5.5, 1.5] }, // 苯环氢
            { symbol: 'H', position: [6.1, 4.2, 1.9] }, // 苯环氢
            { symbol: 'H', position: [6.1, 1.7, 1.9] }, // 苯环氢
            { symbol: 'H', position: [4.0, 0.4, 1.5] }  // 苯环氢
        ],
        bonds: [
            // 主链键
            { atoms: [0, 1], order: 1 }, // N-Cα
            { atoms: [1, 2], order: 1 }, // Cα-C
            { atoms: [2, 3], order: 2 }, // C=O
            { atoms: [2, 4], order: 1 }, // C-O
            { atoms: [0, 5], order: 1 }, // N-H
            { atoms: [0, 6], order: 1 }, // N-H

            // 侧链键
            { atoms: [1, 7], order: 1 }, // Cα-Cβ
            { atoms: [7, 8], order: 1 }, // Cβ-C

            // 苯环键
            { atoms: [8, 9], order: 1 },
            { atoms: [9, 10], order: 2 },
            { atoms: [10, 11], order: 1 },
            { atoms: [11, 12], order: 2 },
            { atoms: [12, 13], order: 1 },
            { atoms: [13, 8], order: 2 },

            // 氢键
            { atoms: [1, 14], order: 1 },
            { atoms: [7, 15], order: 1 },
            { atoms: [7, 16], order: 1 },
            { atoms: [9, 17], order: 1 },
            { atoms: [10, 18], order: 1 },
            { atoms: [11, 19], order: 1 },
            { atoms: [12, 20], order: 1 },
            { atoms: [13, 21], order: 1 }
        ]
    },

    // ==================== 更多神经递质 ====================

    // 血清素 (5-羟色胺)
    serotonin: {
        name: '血清素',
        category: '神经递质',
        formula: 'C₁₀H₁₂N₂O',
        description: '血清素是重要的神经递质，调节情绪、睡眠和食欲，缺乏时可能导致抑郁。',
        atoms: [
            // 吲哚环系统
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.4, 0, 0] },
            { symbol: 'C', position: [2.1, 1.2, 0] },
            { symbol: 'C', position: [1.4, 2.4, 0] },
            { symbol: 'C', position: [0, 2.4, 0] },
            { symbol: 'C', position: [-0.7, 1.2, 0] },
            { symbol: 'C', position: [3.5, 1.2, 0] },
            { symbol: 'N', position: [4.2, 2.4, 0] },
            { symbol: 'C', position: [3.5, 3.6, 0] },
            { symbol: 'C', position: [2.1, 3.6, 0] },

            // 羟基
            { symbol: 'O', position: [-0.7, 3.6, 0] },

            // 乙胺侧链
            { symbol: 'C', position: [4.2, 4.8, 0] },
            { symbol: 'C', position: [5.6, 4.8, 0] },
            { symbol: 'N', position: [6.3, 6.0, 0] },

            // 氢原子
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [1.9, -0.9, 0] },
            { symbol: 'H', position: [-1.8, 1.2, 0] },
            { symbol: 'H', position: [4.0, 0.3, 0] },
            { symbol: 'H', position: [5.2, 2.4, 0] },
            { symbol: 'H', position: [-1.2, 4.4, 0] },
            { symbol: 'H', position: [3.7, 5.7, 0] },
            { symbol: 'H', position: [6.1, 3.9, 0] },
            { symbol: 'H', position: [5.8, 6.9, 0] },
            { symbol: 'H', position: [7.3, 6.0, 0] }
        ],
        bonds: [
            // 吲哚环键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 2 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 2 },
            { atoms: [2, 6], order: 1 },
            { atoms: [6, 7], order: 1 },
            { atoms: [7, 8], order: 2 },
            { atoms: [8, 9], order: 1 },
            { atoms: [9, 3], order: 1 },

            // 羟基键
            { atoms: [4, 10], order: 1 },

            // 侧链键
            { atoms: [8, 11], order: 1 },
            { atoms: [11, 12], order: 1 },
            { atoms: [12, 13], order: 1 },

            // 氢键
            { atoms: [0, 14], order: 1 },
            { atoms: [1, 15], order: 1 },
            { atoms: [5, 16], order: 1 },
            { atoms: [6, 17], order: 1 },
            { atoms: [7, 18], order: 1 },
            { atoms: [10, 19], order: 1 },
            { atoms: [11, 20], order: 1 },
            { atoms: [12, 21], order: 1 },
            { atoms: [13, 22], order: 1 },
            { atoms: [13, 23], order: 1 }
        ]
    },

    // GABA (γ-氨基丁酸)
    gaba: {
        name: 'GABA',
        category: '神经递质',
        formula: 'C₄H₉NO₂',
        description: 'GABA是主要的抑制性神经递质，有助于减少神经元活动，产生镇静效果。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.5, 0, 0] },
            { symbol: 'C', position: [3.0, 0, 0] },
            { symbol: 'C', position: [4.5, 0, 0] },
            { symbol: 'C', position: [6.0, 0, 0] },
            { symbol: 'O', position: [6.7, 1.2, 0] },
            { symbol: 'O', position: [6.7, -1.2, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [1.5, 0.9, 0] },
            { symbol: 'H', position: [1.5, -0.9, 0] },
            { symbol: 'H', position: [3.0, 0.9, 0] },
            { symbol: 'H', position: [3.0, -0.9, 0] },
            { symbol: 'H', position: [4.5, 0.9, 0] },
            { symbol: 'H', position: [4.5, -0.9, 0] },
            { symbol: 'H', position: [7.7, -1.2, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 2 },
            { atoms: [4, 6], order: 1 },
            { atoms: [0, 7], order: 1 },
            { atoms: [0, 8], order: 1 },
            { atoms: [1, 9], order: 1 },
            { atoms: [1, 10], order: 1 },
            { atoms: [2, 11], order: 1 },
            { atoms: [2, 12], order: 1 },
            { atoms: [3, 13], order: 1 },
            { atoms: [3, 14], order: 1 },
            { atoms: [6, 15], order: 1 }
        ]
    },

    // ==================== 药物分子 ====================

    // 阿司匹林
    aspirin: {
        name: '阿司匹林',
        category: '药物分子',
        formula: 'C₉H₈O₄',
        description: '阿司匹林是常用的解热镇痛药，也用于预防心血管疾病。',
        atoms: [
            // 苯环
            { symbol: 'C', position: [1.0, 0, 0] },
            { symbol: 'C', position: [0.5, 0.87, 0] },
            { symbol: 'C', position: [-0.5, 0.87, 0] },
            { symbol: 'C', position: [-1.0, 0, 0] },
            { symbol: 'C', position: [-0.5, -0.87, 0] },
            { symbol: 'C', position: [0.5, -0.87, 0] },

            // 羧基
            { symbol: 'C', position: [2.5, 0, 0] },
            { symbol: 'O', position: [3.2, 1.2, 0] },
            { symbol: 'O', position: [3.2, -1.2, 0] },

            // 乙酰基
            { symbol: 'O', position: [1.0, 1.74, 0] },
            { symbol: 'C', position: [2.0, 2.6, 0] },
            { symbol: 'O', position: [2.0, 3.8, 0] },
            { symbol: 'C', position: [3.2, 1.9, 0] },

            // 氢原子
            { symbol: 'H', position: [-1.0, 1.56, 0] },
            { symbol: 'H', position: [-1.8, 0, 0] },
            { symbol: 'H', position: [-1.0, -1.56, 0] },
            { symbol: 'H', position: [1.0, -1.56, 0] },
            { symbol: 'H', position: [4.2, -1.2, 0] },
            { symbol: 'H', position: [3.2, 0.8, 0] },
            { symbol: 'H', position: [4.1, 2.4, 0] },
            { symbol: 'H', position: [3.2, 2.4, 0.9] },
            { symbol: 'H', position: [3.2, 2.4, -0.9] }
        ],
        bonds: [
            // 苯环键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 2 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 2 },

            // 羧基键
            { atoms: [0, 6], order: 1 },
            { atoms: [6, 7], order: 2 },
            { atoms: [6, 8], order: 1 },

            // 乙酰基键
            { atoms: [1, 9], order: 1 },
            { atoms: [9, 10], order: 1 },
            { atoms: [10, 11], order: 2 },
            { atoms: [10, 12], order: 1 },

            // 氢键
            { atoms: [2, 13], order: 1 },
            { atoms: [3, 14], order: 1 },
            { atoms: [4, 15], order: 1 },
            { atoms: [5, 16], order: 1 },
            { atoms: [8, 17], order: 1 },
            { atoms: [12, 18], order: 1 },
            { atoms: [12, 19], order: 1 },
            { atoms: [12, 20], order: 1 },
            { atoms: [12, 21], order: 1 }
        ]
    },

    // 咖啡因
    caffeine: {
        name: '咖啡因',
        category: '药物分子',
        formula: 'C₈H₁₀N₄O₂',
        description: '咖啡因是一种中枢神经系统兴奋剂，存在于咖啡、茶和可乐中。',
        atoms: [
            // 嘌呤环系统
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.1, 0.7, 0] },
            { symbol: 'N', position: [2.3, 0.1, 0] },
            { symbol: 'C', position: [2.4, -1.2, 0] },
            { symbol: 'C', position: [1.3, -2.0, 0] },
            { symbol: 'C', position: [0.1, -1.4, 0] },
            { symbol: 'N', position: [3.7, -1.7, 0] },
            { symbol: 'C', position: [3.6, -3.1, 0] },
            { symbol: 'N', position: [2.3, -3.4, 0] },

            // 羰基
            { symbol: 'O', position: [1.2, -3.3, 0] },
            { symbol: 'O', position: [-1.0, -2.0, 0] },

            // 甲基
            { symbol: 'C', position: [-1.2, 0.6, 0] },
            { symbol: 'C', position: [3.5, 0.8, 0] },
            { symbol: 'C', position: [2.0, -4.8, 0] },

            // 氢原子
            { symbol: 'H', position: [1.0, 1.8, 0] },
            { symbol: 'H', position: [4.5, -3.7, 0] },
            { symbol: 'H', position: [-1.7, 1.5, 0] },
            { symbol: 'H', position: [-1.8, -0.3, 0] },
            { symbol: 'H', position: [-1.5, 0.9, 0.9] },
            { symbol: 'H', position: [4.4, 0.2, 0] },
            { symbol: 'H', position: [3.8, 1.8, 0] },
            { symbol: 'H', position: [3.2, 0.9, 0.9] },
            { symbol: 'H', position: [1.0, -5.2, 0] },
            { symbol: 'H', position: [2.8, -5.4, 0] },
            { symbol: 'H', position: [2.0, -4.8, 0.9] }
        ],
        bonds: [
            // 嘌呤环键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 1 },
            { atoms: [3, 6], order: 2 },
            { atoms: [6, 7], order: 1 },
            { atoms: [7, 8], order: 2 },
            { atoms: [8, 4], order: 1 },

            // 羰基键
            { atoms: [4, 9], order: 2 },
            { atoms: [5, 10], order: 2 },

            // 甲基键
            { atoms: [0, 11], order: 1 },
            { atoms: [2, 12], order: 1 },
            { atoms: [8, 13], order: 1 },

            // 氢键
            { atoms: [1, 14], order: 1 },
            { atoms: [7, 15], order: 1 },
            { atoms: [11, 16], order: 1 },
            { atoms: [11, 17], order: 1 },
            { atoms: [11, 18], order: 1 },
            { atoms: [12, 19], order: 1 },
            { atoms: [12, 20], order: 1 },
            { atoms: [12, 21], order: 1 },
            { atoms: [13, 22], order: 1 },
            { atoms: [13, 23], order: 1 },
            { atoms: [13, 24], order: 1 }
        ]
    },

    // ==================== 碳水化合物 ====================

    // 葡萄糖 (α-D-葡萄糖)
    glucose: {
        name: '葡萄糖',
        category: '碳水化合物',
        formula: 'C₆H₁₂O₆',
        description: '葡萄糖是最重要的单糖，是细胞能量的主要来源。',
        atoms: [
            // 六元环
            { symbol: 'C', position: [0, 0, 0] },      // C1
            { symbol: 'C', position: [1.4, 0.5, 0] },  // C2
            { symbol: 'C', position: [2.1, -0.7, 0] }, // C3
            { symbol: 'C', position: [1.4, -1.9, 0] }, // C4
            { symbol: 'C', position: [0, -2.4, 0] },   // C5
            { symbol: 'O', position: [-0.7, -1.2, 0] },// O环氧

            // 羟基
            { symbol: 'O', position: [-0.7, 1.2, 0] }, // O1 (α-羟基)
            { symbol: 'O', position: [2.1, 1.7, 0] },  // O2
            { symbol: 'O', position: [3.5, -0.4, 0] }, // O3
            { symbol: 'O', position: [2.1, -3.1, 0] }, // O4

            // 羟甲基
            { symbol: 'C', position: [-0.7, -3.6, 0] },// C6
            { symbol: 'O', position: [-2.1, -3.1, 0] },// O6

            // 氢原子
            { symbol: 'H', position: [0.3, 0.5, 0.9] },  // H1
            { symbol: 'H', position: [1.7, 0.0, 0.9] },  // H2
            { symbol: 'H', position: [1.8, -1.2, 0.9] }, // H3
            { symbol: 'H', position: [1.1, -2.4, 0.9] }, // H4
            { symbol: 'H', position: [0.3, -2.9, 0.9] }, // H5
            { symbol: 'H', position: [-1.2, 1.9, 0] },   // HO1
            { symbol: 'H', position: [2.6, 2.4, 0] },    // HO2
            { symbol: 'H', position: [4.0, 0.3, 0] },    // HO3
            { symbol: 'H', position: [2.6, -3.8, 0] },   // HO4
            { symbol: 'H', position: [-0.4, -4.6, 0] },  // H6a
            { symbol: 'H', position: [-1.0, -3.6, 0.9] },// H6b
            { symbol: 'H', position: [-2.6, -3.8, 0] }   // HO6
        ],
        bonds: [
            // 六元环键
            { atoms: [0, 1], order: 1 },  // C1-C2
            { atoms: [1, 2], order: 1 },  // C2-C3
            { atoms: [2, 3], order: 1 },  // C3-C4
            { atoms: [3, 4], order: 1 },  // C4-C5
            { atoms: [4, 5], order: 1 },  // C5-O
            { atoms: [5, 0], order: 1 },  // O-C1

            // 羟基键
            { atoms: [0, 6], order: 1 },  // C1-O1
            { atoms: [1, 7], order: 1 },  // C2-O2
            { atoms: [2, 8], order: 1 },  // C3-O3
            { atoms: [3, 9], order: 1 },  // C4-O4

            // 羟甲基键
            { atoms: [4, 10], order: 1 }, // C5-C6
            { atoms: [10, 11], order: 1 },// C6-O6

            // 氢键
            { atoms: [0, 12], order: 1 },
            { atoms: [1, 13], order: 1 },
            { atoms: [2, 14], order: 1 },
            { atoms: [3, 15], order: 1 },
            { atoms: [4, 16], order: 1 },
            { atoms: [6, 17], order: 1 },
            { atoms: [7, 18], order: 1 },
            { atoms: [8, 19], order: 1 },
            { atoms: [9, 20], order: 1 },
            { atoms: [10, 21], order: 1 },
            { atoms: [10, 22], order: 1 },
            { atoms: [11, 23], order: 1 }
        ]
    },

    // ==================== 维生素 ====================

    // 维生素C (抗坏血酸)
    vitaminC: {
        name: '维生素C',
        category: '维生素',
        formula: 'C₆H₈O₆',
        description: '维生素C是重要的抗氧化剂，人体无法自行合成，必须从食物中获取。',
        atoms: [
            // 五元环内酯
            { symbol: 'C', position: [0, 0, 0] },      // C1
            { symbol: 'C', position: [1.4, 0, 0] },    // C2
            { symbol: 'C', position: [2.1, 1.2, 0] },  // C3
            { symbol: 'O', position: [1.4, 2.4, 0] },  // O内酯
            { symbol: 'C', position: [0, 2.4, 0] },    // C4
            { symbol: 'C', position: [-0.7, 1.2, 0] }, // C5

            // 羟基和羰基
            { symbol: 'O', position: [-0.7, -1.2, 0] },// O1 (羟基)
            { symbol: 'O', position: [2.1, -1.2, 0] }, // O2 (羟基)
            { symbol: 'O', position: [3.5, 1.2, 0] },  // O3 (羰基)
            { symbol: 'O', position: [-0.7, 3.6, 0] }, // O4 (羟基)

            // 羟甲基
            { symbol: 'C', position: [-2.1, 1.2, 0] }, // C6
            { symbol: 'O', position: [-2.8, 2.4, 0] }, // O6 (羟基)

            // 氢原子
            { symbol: 'H', position: [-1.2, -1.9, 0] },// HO1
            { symbol: 'H', position: [2.6, -1.9, 0] }, // HO2
            { symbol: 'H', position: [-1.2, 4.3, 0] }, // HO4
            { symbol: 'H', position: [-2.4, 0.2, 0] }, // H6a
            { symbol: 'H', position: [-2.4, 1.2, 0.9] },// H6b
            { symbol: 'H', position: [-3.3, 3.1, 0] }  // HO6
        ],
        bonds: [
            // 五元环键
            { atoms: [0, 1], order: 1 },  // C1-C2
            { atoms: [1, 2], order: 1 },  // C2-C3
            { atoms: [2, 3], order: 1 },  // C3-O
            { atoms: [3, 4], order: 1 },  // O-C4
            { atoms: [4, 5], order: 1 },  // C4-C5
            { atoms: [5, 0], order: 1 },  // C5-C1

            // 羟基和羰基键
            { atoms: [0, 6], order: 1 },  // C1-O1
            { atoms: [1, 7], order: 1 },  // C2-O2
            { atoms: [2, 8], order: 2 },  // C3=O3
            { atoms: [4, 9], order: 1 },  // C4-O4

            // 羟甲基键
            { atoms: [5, 10], order: 1 }, // C5-C6
            { atoms: [10, 11], order: 1 },// C6-O6

            // 氢键
            { atoms: [6, 12], order: 1 },
            { atoms: [7, 13], order: 1 },
            { atoms: [9, 14], order: 1 },
            { atoms: [10, 15], order: 1 },
            { atoms: [10, 16], order: 1 },
            { atoms: [11, 17], order: 1 }
        ]
    },

    // ==================== 脂质 ====================

    // 胆固醇
    cholesterol: {
        name: '胆固醇',
        category: '脂质',
        formula: 'C₂₇H₄₆O',
        description: '胆固醇是细胞膜的重要组成部分，也是合成胆汁酸和激素的前体。',
        atoms: [
            // 甾体骨架 (简化表示)
            { symbol: 'C', position: [0, 0, 0] },      // C1
            { symbol: 'C', position: [1.4, 0.5, 0] },  // C2
            { symbol: 'C', position: [2.8, 0, 0] },    // C3
            { symbol: 'C', position: [4.2, 0.5, 0] },  // C4
            { symbol: 'C', position: [5.6, 0, 0] },    // C5
            { symbol: 'C', position: [7.0, 0.5, 0] },  // C6
            { symbol: 'C', position: [8.4, 0, 0] },    // C7
            { symbol: 'C', position: [9.8, 0.5, 0] },  // C8

            // 羟基
            { symbol: 'O', position: [2.8, -1.4, 0] }, // O3 (羟基)

            // 甲基
            { symbol: 'C', position: [11.2, 0, 0] },   // C甲基1
            { symbol: 'C', position: [12.6, 0.5, 0] }, // C甲基2

            // 氢原子 (部分)
            { symbol: 'H', position: [0, 1.0, 0] },
            { symbol: 'H', position: [1.4, 1.5, 0] },
            { symbol: 'H', position: [3.3, -2.1, 0] }, // HO3
            { symbol: 'H', position: [11.2, -1.0, 0] },
            { symbol: 'H', position: [13.6, 0, 0] }
        ],
        bonds: [
            // 甾体骨架键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 2 },
            { atoms: [5, 6], order: 1 },
            { atoms: [6, 7], order: 1 },

            // 羟基键
            { atoms: [2, 8], order: 1 },

            // 甲基键
            { atoms: [7, 9], order: 1 },
            { atoms: [9, 10], order: 1 },

            // 氢键
            { atoms: [0, 11], order: 1 },
            { atoms: [1, 12], order: 1 },
            { atoms: [8, 13], order: 1 },
            { atoms: [9, 14], order: 1 },
            { atoms: [10, 15], order: 1 }
        ]
    },

    // ==================== 更多核苷酸 ====================

    // 鸟嘌呤 - 核苷酸
    guanine: {
        name: '鸟嘌呤',
        category: '核苷酸',
        formula: 'C₅H₅N₅O',
        description: '鸟嘌呤是DNA和RNA的重要组成部分，与胞嘧啶配对。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.1, 0.7, 0] },
            { symbol: 'N', position: [2.3, 0.1, 0] },
            { symbol: 'C', position: [2.4, -1.2, 0] },
            { symbol: 'C', position: [1.3, -2.0, 0] },
            { symbol: 'C', position: [0.1, -1.4, 0] },
            { symbol: 'N', position: [3.7, -1.7, 0] },
            { symbol: 'C', position: [3.6, -3.1, 0] },
            { symbol: 'N', position: [2.3, -3.4, 0] },
            { symbol: 'N', position: [-1.1, -2.0, 0] },
            { symbol: 'O', position: [1.2, -3.3, 0] },
            { symbol: 'H', position: [1.0, 1.8, 0] },
            { symbol: 'H', position: [4.5, -3.7, 0] },
            { symbol: 'H', position: [2.0, -4.4, 0] },
            { symbol: 'H', position: [-1.9, -1.4, 0] },
            { symbol: 'H', position: [-1.2, -3.0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 2 },
            { atoms: [5, 0], order: 1 },
            { atoms: [3, 6], order: 2 },
            { atoms: [6, 7], order: 1 },
            { atoms: [7, 8], order: 2 },
            { atoms: [8, 4], order: 1 },
            { atoms: [5, 9], order: 1 },
            { atoms: [4, 10], order: 2 },
            { atoms: [1, 11], order: 1 },
            { atoms: [7, 12], order: 1 },
            { atoms: [8, 13], order: 1 },
            { atoms: [9, 14], order: 1 },
            { atoms: [9, 15], order: 1 }
        ]
    },

    // 胞嘧啶 - 核苷酸
    cytosine: {
        name: '胞嘧啶',
        category: '核苷酸',
        formula: 'C₄H₅N₃O',
        description: '胞嘧啶是DNA和RNA的重要组成部分，与鸟嘌呤配对。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.1, 0.7, 0] },
            { symbol: 'N', position: [2.3, 0.1, 0] },
            { symbol: 'C', position: [2.4, -1.2, 0] },
            { symbol: 'C', position: [1.3, -2.0, 0] },
            { symbol: 'C', position: [0.1, -1.4, 0] },
            { symbol: 'O', position: [1.0, 2.0, 0] },
            { symbol: 'N', position: [3.6, -1.7, 0] },
            { symbol: 'H', position: [-0.9, 0.4, 0] },
            { symbol: 'H', position: [3.1, 0.7, 0] },
            { symbol: 'H', position: [-0.8, -2.0, 0] },
            { symbol: 'H', position: [4.4, -1.2, 0] },
            { symbol: 'H', position: [3.7, -2.7, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 2 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 2 },
            { atoms: [1, 6], order: 2 },
            { atoms: [3, 7], order: 1 },
            { atoms: [0, 8], order: 1 },
            { atoms: [2, 9], order: 1 },
            { atoms: [5, 10], order: 1 },
            { atoms: [7, 11], order: 1 },
            { atoms: [7, 12], order: 1 }
        ]
    },

    // ==================== 更多氨基酸 ====================

    // 丝氨酸 (Serine)
    serine: {
        name: '丝氨酸',
        category: '氨基酸',
        formula: 'C₃H₇NO₃',
        description: '丝氨酸是一种含羟基的氨基酸，参与蛋白质磷酸化修饰。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] },
            { symbol: 'C', position: [2.3, -0.6, 0] },
            { symbol: 'O', position: [2.0, -1.8, 0] },
            { symbol: 'O', position: [3.5, -0.3, 0] },
            { symbol: 'H', position: [-0.3, 0.9, 0] },
            { symbol: 'H', position: [-0.6, -0.7, 0] },
            { symbol: 'C', position: [1.5, 1.5, 1.1] },
            { symbol: 'O', position: [2.8, 1.8, 1.3] },
            { symbol: 'H', position: [1.3, 1.0, 0] },
            { symbol: 'H', position: [0.7, 2.2, 1.3] },
            { symbol: 'H', position: [3.3, 2.5, 1.0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 2 },
            { atoms: [2, 4], order: 1 },
            { atoms: [0, 5], order: 1 },
            { atoms: [0, 6], order: 1 },
            { atoms: [1, 7], order: 1 },
            { atoms: [7, 8], order: 1 },
            { atoms: [1, 9], order: 1 },
            { atoms: [7, 10], order: 1 },
            { atoms: [8, 11], order: 1 }
        ]
    },

    // 半胱氨酸 (Cysteine)
    cysteine: {
        name: '半胱氨酸',
        category: '氨基酸',
        formula: 'C₃H₇NOS',
        description: '半胱氨酸含有硫醇基团，能形成二硫键，对蛋白质结构稳定性很重要。',
        atoms: [
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] },
            { symbol: 'C', position: [2.3, -0.6, 0] },
            { symbol: 'O', position: [2.0, -1.8, 0] },
            { symbol: 'O', position: [3.5, -0.3, 0] },
            { symbol: 'H', position: [-0.3, 0.9, 0] },
            { symbol: 'H', position: [-0.6, -0.7, 0] },
            { symbol: 'C', position: [1.5, 1.5, 1.1] },
            { symbol: 'S', position: [2.8, 2.2, 1.3] },
            { symbol: 'H', position: [1.3, 1.0, 0] },
            { symbol: 'H', position: [0.7, 2.2, 1.3] },
            { symbol: 'H', position: [1.3, 0.9, 2.0] },
            { symbol: 'H', position: [3.5, 1.5, 1.8] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 2 },
            { atoms: [2, 4], order: 1 },
            { atoms: [0, 5], order: 1 },
            { atoms: [0, 6], order: 1 },
            { atoms: [1, 7], order: 1 },
            { atoms: [7, 8], order: 1 },
            { atoms: [1, 9], order: 1 },
            { atoms: [7, 10], order: 1 },
            { atoms: [7, 11], order: 1 },
            { atoms: [8, 12], order: 1 }
        ]
    },

    // ==================== 更多药物分子 ====================

    // 布洛芬 (Ibuprofen)
    ibuprofen: {
        name: '布洛芬',
        category: '药物分子',
        formula: 'C₁₃H₁₈O₂',
        description: '布洛芬是一种非甾体抗炎药，用于缓解疼痛、发热和炎症。',
        atoms: [
            // 苯环
            { symbol: 'C', position: [1.0, 0, 0] },
            { symbol: 'C', position: [0.5, 0.87, 0] },
            { symbol: 'C', position: [-0.5, 0.87, 0] },
            { symbol: 'C', position: [-1.0, 0, 0] },
            { symbol: 'C', position: [-0.5, -0.87, 0] },
            { symbol: 'C', position: [0.5, -0.87, 0] },

            // 异丁基侧链
            { symbol: 'C', position: [2.5, 0, 0] },
            { symbol: 'C', position: [3.2, 1.2, 0] },
            { symbol: 'C', position: [4.7, 1.0, 0] },
            { symbol: 'C', position: [2.8, 2.6, 0] },

            // 丙酸基
            { symbol: 'C', position: [-2.5, 0, 0] },
            { symbol: 'C', position: [-3.2, 1.2, 0] },
            { symbol: 'C', position: [-4.7, 1.0, 0] },
            { symbol: 'O', position: [-5.4, 2.2, 0] },
            { symbol: 'O', position: [-5.4, -0.2, 0] },

            // 甲基
            { symbol: 'C', position: [-2.8, 2.6, 0] },

            // 氢原子
            { symbol: 'H', position: [1.0, 1.56, 0] },
            { symbol: 'H', position: [-1.0, 1.56, 0] },
            { symbol: 'H', position: [-1.8, 0, 0] },
            { symbol: 'H', position: [-1.0, -1.56, 0] },
            { symbol: 'H', position: [1.0, -1.56, 0] },
            { symbol: 'H', position: [2.8, -0.9, 0] },
            { symbol: 'H', position: [5.2, 1.9, 0] },
            { symbol: 'H', position: [2.3, 3.5, 0] },
            { symbol: 'H', position: [-3.5, -0.7, 0] },
            { symbol: 'H', position: [-6.3, 2.0, 0] },
            { symbol: 'H', position: [-2.3, 3.5, 0] }
        ],
        bonds: [
            // 苯环键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 2 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 2 },

            // 异丁基侧链键
            { atoms: [0, 6], order: 1 },
            { atoms: [6, 7], order: 1 },
            { atoms: [7, 8], order: 1 },
            { atoms: [7, 9], order: 1 },

            // 丙酸基键
            { atoms: [3, 10], order: 1 },
            { atoms: [10, 11], order: 1 },
            { atoms: [11, 12], order: 1 },
            { atoms: [12, 13], order: 2 },
            { atoms: [12, 14], order: 1 },
            { atoms: [11, 15], order: 1 },

            // 氢键
            { atoms: [1, 16], order: 1 },
            { atoms: [2, 17], order: 1 },
            { atoms: [4, 19], order: 1 },
            { atoms: [5, 20], order: 1 },
            { atoms: [6, 21], order: 1 },
            { atoms: [8, 22], order: 1 },
            { atoms: [9, 23], order: 1 },
            { atoms: [10, 24], order: 1 },
            { atoms: [13, 25], order: 1 },
            { atoms: [15, 26], order: 1 }
        ]
    },

    // 尼古丁 (Nicotine)
    nicotine: {
        name: '尼古丁',
        category: '药物分子',
        formula: 'C₁₀H₁₄N₂',
        description: '尼古丁是烟草中的主要生物碱，具有成瘾性，作用于神经系统。',
        atoms: [
            // 吡啶环
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.1, 0.7, 0] },
            { symbol: 'C', position: [2.3, 0.1, 0] },
            { symbol: 'C', position: [2.4, -1.2, 0] },
            { symbol: 'C', position: [1.3, -2.0, 0] },
            { symbol: 'C', position: [0.1, -1.4, 0] },

            // 吡咯烷环
            { symbol: 'C', position: [3.7, 0.8, 0] },
            { symbol: 'N', position: [4.8, 0.0, 0] },
            { symbol: 'C', position: [4.8, -1.5, 0] },
            { symbol: 'C', position: [3.7, -1.9, 0] },

            // 甲基
            { symbol: 'C', position: [6.0, 0.5, 0] },

            // 氢原子
            { symbol: 'H', position: [1.0, 1.8, 0] },
            { symbol: 'H', position: [1.2, -3.1, 0] },
            { symbol: 'H', position: [-0.8, -2.0, 0] },
            { symbol: 'H', position: [3.8, 1.9, 0] },
            { symbol: 'H', position: [5.7, -1.9, 0] },
            { symbol: 'H', position: [3.6, -3.0, 0] },
            { symbol: 'H', position: [6.9, 0.0, 0] },
            { symbol: 'H', position: [6.0, 1.6, 0] },
            { symbol: 'H', position: [6.0, 0.2, 0.9] }
        ],
        bonds: [
            // 吡啶环键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 2 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 2 },

            // 连接键
            { atoms: [2, 6], order: 1 },
            { atoms: [3, 9], order: 1 },

            // 吡咯烷环键
            { atoms: [6, 7], order: 1 },
            { atoms: [7, 8], order: 1 },
            { atoms: [8, 9], order: 1 },

            // 甲基键
            { atoms: [7, 10], order: 1 },

            // 氢键
            { atoms: [1, 11], order: 1 },
            { atoms: [4, 12], order: 1 },
            { atoms: [5, 13], order: 1 },
            { atoms: [6, 14], order: 1 },
            { atoms: [8, 15], order: 1 },
            { atoms: [9, 16], order: 1 },
            { atoms: [10, 17], order: 1 },
            { atoms: [10, 18], order: 1 },
            { atoms: [10, 19], order: 1 }
        ]
    },

    // ==================== 反应中需要的分子 ====================

    // 氢氧化钠 (NaOH)
    sodiumHydroxide: {
        name: '氢氧化钠',
        category: '无机分子',
        formula: 'NaOH',
        description: '氢氧化钠是强碱，常用于酸碱中和反应。',
        atoms: [
            { symbol: 'Na', position: [0, 0, 0] },
            { symbol: 'O', position: [1.9, 0, 0] },
            { symbol: 'H', position: [2.8, 0.7, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 }
        ]
    },

    // 氯化钠 (NaCl)
    sodiumChloride: {
        name: '氯化钠',
        category: '无机分子',
        formula: 'NaCl',
        description: '氯化钠是食盐的主要成分，由酸碱中和反应产生。',
        atoms: [
            { symbol: 'Na', position: [0, 0, 0] },
            { symbol: 'Cl', position: [2.3, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 }
        ]
    },

    // 氧气 (O2)
    oxygen: {
        name: '氧气',
        category: '无机分子',
        formula: 'O₂',
        description: '氧气是燃烧反应的重要反应物。',
        atoms: [
            { symbol: 'O', position: [0, 0, 0] },
            { symbol: 'O', position: [1.2, 0, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 2 }
        ]
    },

    // 乙酸 (醋酸)
    aceticAcid: {
        name: '乙酸',
        category: '有机小分子',
        formula: 'CH₃COOH',
        description: '乙酸是醋的主要成分，可与醇类发生酯化反应。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.5, 0, 0] },
            { symbol: 'O', position: [2.2, 1.2, 0] },
            { symbol: 'O', position: [2.2, -1.2, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [-0.5, 0, 0.9] },
            { symbol: 'H', position: [3.1, -1.2, 0] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [1, 3], order: 1 },
            { atoms: [0, 4], order: 1 },
            { atoms: [0, 5], order: 1 },
            { atoms: [0, 6], order: 1 },
            { atoms: [3, 7], order: 1 }
        ]
    },

    // 乙酸乙酯
    ethylAcetate: {
        name: '乙酸乙酯',
        category: '有机小分子',
        formula: 'CH₃COOC₂H₅',
        description: '乙酸乙酯是酯化反应的产物，具有果香味。',
        atoms: [
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [1.5, 0, 0] },
            { symbol: 'O', position: [2.2, 1.2, 0] },
            { symbol: 'O', position: [2.2, -1.2, 0] },
            { symbol: 'C', position: [3.6, -1.2, 0] },
            { symbol: 'C', position: [4.3, 0, 0] },
            { symbol: 'H', position: [-0.5, 0.9, 0] },
            { symbol: 'H', position: [-0.5, -0.9, 0] },
            { symbol: 'H', position: [-0.5, 0, 0.9] },
            { symbol: 'H', position: [3.9, -2.1, 0] },
            { symbol: 'H', position: [3.9, -0.3, 0] },
            { symbol: 'H', position: [5.3, 0, 0] },
            { symbol: 'H', position: [4.0, 0.9, 0] },
            { symbol: 'H', position: [4.0, 0, 0.9] }
        ],
        bonds: [
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 2 },
            { atoms: [1, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 1 },
            { atoms: [0, 6], order: 1 },
            { atoms: [0, 7], order: 1 },
            { atoms: [0, 8], order: 1 },
            { atoms: [4, 9], order: 1 },
            { atoms: [4, 10], order: 1 },
            { atoms: [5, 11], order: 1 },
            { atoms: [5, 12], order: 1 },
            { atoms: [5, 13], order: 1 }
        ]
    },

    // 甘氨酰丙氨酸 (二肽)
    glycylAlanine: {
        name: '甘氨酰丙氨酸',
        category: '氨基酸',
        formula: 'C₅H₁₀N₂O₃',
        description: '甘氨酰丙氨酸是由甘氨酸和丙氨酸形成的二肽。',
        atoms: [
            // 甘氨酸部分
            { symbol: 'N', position: [0, 0, 0] },
            { symbol: 'C', position: [1.32, 0.5, 0] },
            { symbol: 'C', position: [2.3, -0.6, 0] },
            { symbol: 'O', position: [2.0, -1.8, 0] },

            // 肽键
            { symbol: 'N', position: [3.5, -0.3, 0] },

            // 丙氨酸部分
            { symbol: 'C', position: [4.82, 0.2, 0] },
            { symbol: 'C', position: [5.8, -0.9, 0] },
            { symbol: 'O', position: [5.5, -2.1, 0] },
            { symbol: 'O', position: [7.0, -0.6, 0] },
            { symbol: 'C', position: [5.0, 1.2, 1.1] },

            // 氢原子
            { symbol: 'H', position: [-0.3, 0.9, 0] },
            { symbol: 'H', position: [-0.6, -0.7, 0] },
            { symbol: 'H', position: [1.3, 1.0, 0] },
            { symbol: 'H', position: [1.0, 1.2, 0.8] },
            { symbol: 'H', position: [3.8, 0.6, 0] },
            { symbol: 'H', position: [4.8, 0.7, 0] },
            { symbol: 'H', position: [7.7, -1.3, 0] },
            { symbol: 'H', position: [4.2, 1.9, 1.3] },
            { symbol: 'H', position: [5.0, 0.7, 2.0] },
            { symbol: 'H', position: [6.0, 1.6, 1.2] }
        ],
        bonds: [
            // 甘氨酸键
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 2 },

            // 肽键
            { atoms: [2, 4], order: 1 },
            { atoms: [4, 5], order: 1 },

            // 丙氨酸键
            { atoms: [5, 6], order: 1 },
            { atoms: [6, 7], order: 2 },
            { atoms: [6, 8], order: 1 },
            { atoms: [5, 9], order: 1 },

            // 氢键
            { atoms: [0, 10], order: 1 },
            { atoms: [0, 11], order: 1 },
            { atoms: [1, 12], order: 1 },
            { atoms: [1, 13], order: 1 },
            { atoms: [4, 14], order: 1 },
            { atoms: [5, 15], order: 1 },
            { atoms: [8, 16], order: 1 },
            { atoms: [9, 17], order: 1 },
            { atoms: [9, 18], order: 1 },
            { atoms: [9, 19], order: 1 }
        ]
    },

    // ==================== 复杂生物分子 ====================

    // DNA双螺旋片段 (真实螺旋结构)
    dnaDoubleHelix: {
        name: 'DNA双螺旋',
        category: '核酸',
        formula: 'DNA',
        description: 'DNA双螺旋结构，展示真实的螺旋形态、碱基配对和磷酸骨架。',
        atoms: (() => {
            const atoms = [];
            const radius = 1.0;        // 螺旋半径
            const pitch = 3.4;         // 螺旋间距 (3.4Å)
            const angleStep = 36;      // 每层旋转36度
            const layers = 10;         // 10层碱基对

            // 生成第一条链 (右手螺旋)
            for (let i = 0; i < layers; i++) {
                const angle = (i * angleStep) * Math.PI / 180; // 转换为弧度
                const y = i * pitch;

                // 磷酸基团 (外侧)
                const px = Math.cos(angle) * (radius + 0.8);
                const pz = Math.sin(angle) * (radius + 0.8);
                atoms.push({ symbol: 'P', position: [px, y, pz] });

                // 糖基团 (中间)
                const sx = Math.cos(angle) * radius;
                const sz = Math.sin(angle) * radius;
                atoms.push({ symbol: 'C', position: [sx, y, sz] });

                // 碱基 (内侧)
                const bx = Math.cos(angle) * (radius - 0.5);
                const bz = Math.sin(angle) * (radius - 0.5);
                const baseType = ['N', 'N', 'N', 'N'][i % 4]; // A, T, G, C
                atoms.push({ symbol: baseType, position: [bx, y, bz] });
            }

            // 生成第二条链 (左手螺旋，反向)
            for (let i = 0; i < layers; i++) {
                const angle = (i * angleStep + 180) * Math.PI / 180; // 180度相位差
                const y = i * pitch;

                // 磷酸基团 (外侧)
                const px = Math.cos(angle) * (radius + 0.8);
                const pz = Math.sin(angle) * (radius + 0.8);
                atoms.push({ symbol: 'P', position: [px, y, pz] });

                // 糖基团 (中间)
                const sx = Math.cos(angle) * radius;
                const sz = Math.sin(angle) * radius;
                atoms.push({ symbol: 'C', position: [sx, y, sz] });

                // 碱基 (内侧) - 互补配对
                const bx = Math.cos(angle) * (radius - 0.5);
                const bz = Math.sin(angle) * (radius - 0.5);
                const baseType = ['N', 'N', 'N', 'N'][i % 4]; // T, A, C, G (互补)
                atoms.push({ symbol: baseType, position: [bx, y, bz] });
            }

            return atoms;
        })(),
        bonds: (() => {
            const bonds = [];
            const layers = 10;

            // 第一条链骨架连接 (P-C-N 模式)
            for (let i = 0; i < layers; i++) {
                const baseIndex = i * 3;
                bonds.push({ atoms: [baseIndex, baseIndex + 1], order: 1 });     // P-C
                bonds.push({ atoms: [baseIndex + 1, baseIndex + 2], order: 1 }); // C-N

                // 连接到下一层
                if (i < layers - 1) {
                    bonds.push({ atoms: [baseIndex, baseIndex + 3], order: 1 }); // P-P连接
                }
            }

            // 第二条链骨架连接
            for (let i = 0; i < layers; i++) {
                const baseIndex = layers * 3 + i * 3; // 第二条链起始位置
                bonds.push({ atoms: [baseIndex, baseIndex + 1], order: 1 });     // P-C
                bonds.push({ atoms: [baseIndex + 1, baseIndex + 2], order: 1 }); // C-N

                // 连接到下一层
                if (i < layers - 1) {
                    bonds.push({ atoms: [baseIndex, baseIndex + 3], order: 1 }); // P-P连接
                }
            }

            // 碱基配对 (氢键) - 连接两条链的碱基
            for (let i = 0; i < layers; i++) {
                const base1Index = i * 3 + 2;           // 第一条链的碱基
                const base2Index = layers * 3 + i * 3 + 2; // 第二条链的碱基
                bonds.push({ atoms: [base1Index, base2Index], order: 1 }); // 碱基配对
            }

            return bonds;
        })()
    },

    // 噬菌体头部 (简化模型)
    bacteriophageHead: {
        name: '噬菌体头部',
        category: '病毒结构',
        formula: 'Viral Capsid',
        description: '噬菌体头部的简化二十面体结构，包含DNA。',
        atoms: [
            // 二十面体顶点 (蛋白质外壳)
            { symbol: 'C', position: [0, 2, 0] },      // 顶部
            { symbol: 'C', position: [1.9, 0.6, 0] },
            { symbol: 'C', position: [1.2, -1.6, 0] },
            { symbol: 'C', position: [-1.2, -1.6, 0] },
            { symbol: 'C', position: [-1.9, 0.6, 0] },

            // 中层
            { symbol: 'C', position: [0, 1, 1.7] },
            { symbol: 'C', position: [1.6, -0.5, 1.7] },
            { symbol: 'C', position: [-1.6, -0.5, 1.7] },
            { symbol: 'C', position: [0, 1, -1.7] },
            { symbol: 'C', position: [1.6, -0.5, -1.7] },
            { symbol: 'C', position: [-1.6, -0.5, -1.7] },

            // 底部
            { symbol: 'C', position: [0, -2, 0] },

            // 内部DNA (简化表示)
            { symbol: 'P', position: [0, 0, 0] },      // DNA中心
            { symbol: 'N', position: [0.5, 0.5, 0.5] },
            { symbol: 'N', position: [-0.5, -0.5, -0.5] },
            { symbol: 'N', position: [0.5, -0.5, 0.5] },
            { symbol: 'N', position: [-0.5, 0.5, -0.5] }
        ],
        bonds: [
            // 外壳结构
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 0], order: 1 },

            // 中层连接
            { atoms: [0, 5], order: 1 },
            { atoms: [1, 6], order: 1 },
            { atoms: [4, 7], order: 1 },
            { atoms: [0, 8], order: 1 },
            { atoms: [1, 9], order: 1 },
            { atoms: [4, 10], order: 1 },

            // 底部连接
            { atoms: [2, 11], order: 1 },
            { atoms: [3, 11], order: 1 },
            { atoms: [6, 11], order: 1 },
            { atoms: [7, 11], order: 1 },

            // DNA连接
            { atoms: [12, 13], order: 1 },
            { atoms: [12, 14], order: 1 },
            { atoms: [12, 15], order: 1 },
            { atoms: [12, 16], order: 1 }
        ]
    },

    // 噬菌体尾部
    bacteriophageTail: {
        name: '噬菌体尾部',
        category: '病毒结构',
        formula: 'Viral Tail',
        description: '噬菌体尾部结构，用于附着和注射DNA。',
        atoms: [
            // 尾管
            { symbol: 'C', position: [0, 0, 0] },
            { symbol: 'C', position: [0, -1, 0] },
            { symbol: 'C', position: [0, -2, 0] },
            { symbol: 'C', position: [0, -3, 0] },
            { symbol: 'C', position: [0, -4, 0] },
            { symbol: 'C', position: [0, -5, 0] },

            // 尾纤维 (6个)
            { symbol: 'C', position: [1.5, -5, 0] },
            { symbol: 'C', position: [2.5, -6, 0] },
            { symbol: 'C', position: [-1.5, -5, 0] },
            { symbol: 'C', position: [-2.5, -6, 0] },
            { symbol: 'C', position: [0, -5, 1.5] },
            { symbol: 'C', position: [0, -6, 2.5] },
            { symbol: 'C', position: [0, -5, -1.5] },
            { symbol: 'C', position: [0, -6, -2.5] },
            { symbol: 'C', position: [1.1, -5, 1.1] },
            { symbol: 'C', position: [1.8, -6, 1.8] },
            { symbol: 'C', position: [-1.1, -5, -1.1] },
            { symbol: 'C', position: [-1.8, -6, -1.8] }
        ],
        bonds: [
            // 尾管
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 1 },

            // 尾纤维
            { atoms: [5, 6], order: 1 },
            { atoms: [6, 7], order: 1 },
            { atoms: [5, 8], order: 1 },
            { atoms: [8, 9], order: 1 },
            { atoms: [5, 10], order: 1 },
            { atoms: [10, 11], order: 1 },
            { atoms: [5, 12], order: 1 },
            { atoms: [12, 13], order: 1 },
            { atoms: [5, 14], order: 1 },
            { atoms: [14, 15], order: 1 },
            { atoms: [5, 16], order: 1 },
            { atoms: [16, 17], order: 1 }
        ]
    },

    // ATP合酶 (分子发动机)
    atpSynthase: {
        name: 'ATP合酶',
        category: '分子发动机',
        formula: 'ATP Synthase',
        description: 'ATP合酶是细胞的分子发动机，利用质子梯度合成ATP。',
        atoms: [
            // F1部分 (头部)
            { symbol: 'C', position: [0, 2, 0] },      // α亚基
            { symbol: 'C', position: [1.5, 1, 0] },    // β亚基
            { symbol: 'C', position: [1.5, -1, 0] },   // β亚基
            { symbol: 'C', position: [0, -2, 0] },     // α亚基
            { symbol: 'C', position: [-1.5, -1, 0] },  // β亚基
            { symbol: 'C', position: [-1.5, 1, 0] },   // α亚基
            { symbol: 'C', position: [0, 0, 0] },      // γ亚基 (旋转轴)

            // F0部分 (膜内)
            { symbol: 'C', position: [0, 0, -2] },     // c环
            { symbol: 'C', position: [1, 0, -2] },
            { symbol: 'C', position: [0.5, 0.9, -2] },
            { symbol: 'C', position: [-0.5, 0.9, -2] },
            { symbol: 'C', position: [-1, 0, -2] },
            { symbol: 'C', position: [-0.5, -0.9, -2] },
            { symbol: 'C', position: [0.5, -0.9, -2] },

            // a亚基
            { symbol: 'C', position: [2, 0, -2] },
            { symbol: 'C', position: [2.5, 1, -2] },
            { symbol: 'C', position: [2.5, -1, -2] },

            // ATP/ADP结合位点
            { symbol: 'P', position: [1.2, 0.5, 0.5] },  // ATP
            { symbol: 'P', position: [1.2, -0.5, 0.5] }, // ADP
            { symbol: 'P', position: [-1.2, 0, 0.5] },   // Pi

            // 质子通道
            { symbol: 'H', position: [2.2, 0.5, -1.5] },
            { symbol: 'H', position: [2.2, -0.5, -1.5] }
        ],
        bonds: [
            // F1部分
            { atoms: [0, 1], order: 1 },
            { atoms: [1, 2], order: 1 },
            { atoms: [2, 3], order: 1 },
            { atoms: [3, 4], order: 1 },
            { atoms: [4, 5], order: 1 },
            { atoms: [5, 0], order: 1 },

            // γ亚基连接
            { atoms: [6, 0], order: 1 },
            { atoms: [6, 2], order: 1 },
            { atoms: [6, 4], order: 1 },

            // F0部分 c环
            { atoms: [7, 8], order: 1 },
            { atoms: [8, 9], order: 1 },
            { atoms: [9, 10], order: 1 },
            { atoms: [10, 11], order: 1 },
            { atoms: [11, 12], order: 1 },
            { atoms: [12, 13], order: 1 },
            { atoms: [13, 7], order: 1 },

            // a亚基
            { atoms: [14, 15], order: 1 },
            { atoms: [14, 16], order: 1 },
            { atoms: [15, 16], order: 1 },

            // F1-F0连接
            { atoms: [6, 7], order: 1 },

            // ATP结合
            { atoms: [1, 17], order: 1 },
            { atoms: [2, 18], order: 1 },
            { atoms: [4, 19], order: 1 },

            // 质子通道
            { atoms: [14, 20], order: 1 },
            { atoms: [14, 21], order: 1 }
        ]
    }
};
