/**
 * 备用渲染器
 * 当Three.js渲染失败时提供基本的2D渲染
 */
class FallbackRenderer {
    constructor(container) {
        this.container = container;
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.isActive = false;
        
        // 设置画布大小
        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight;
        this.canvas.style.display = 'none'; // 默认隐藏
        
        container.appendChild(this.canvas);
        
        // 添加窗口大小变化事件监听器
        window.addEventListener('resize', () => this._onResize());
    }
    
    /**
     * 激活备用渲染器
     * @param {string} message - 显示的错误消息
     */
    activate(message = '3D渲染失败，显示备用视图') {
        this.isActive = true;
        this.canvas.style.display = 'block';
        this.errorMessage = message;
        this._render();
        
        console.warn('已激活备用渲染器:', message);
    }
    
    /**
     * 渲染简单的2D分子表示
     * @param {Object} molecule - 分子数据
     */
    renderMolecule(molecule) {
        if (!this.isActive || !molecule) return;
        
        this._clear();
        
        // 绘制标题
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(molecule.name || '分子结构', this.canvas.width / 2, 40);
        
        // 绘制分子信息
        if (molecule.formula) {
            this.ctx.font = '18px Arial';
            this.ctx.fillText(molecule.formula, this.canvas.width / 2, 70);
        }
        
        // 绘制简单的分子表示
        if (molecule.atoms && molecule.atoms.length > 0) {
            this._renderSimpleMolecule(molecule);
        } else {
            // 绘制占位符
            this.ctx.font = '16px Arial';
            this.ctx.fillText('无法显示3D分子结构', this.canvas.width / 2, this.canvas.height / 2);
            this.ctx.fillText('请检查控制台错误或刷新页面', this.canvas.width / 2, this.canvas.height / 2 + 30);
        }
    }
    
    /**
     * 绘制简单的2D分子表示
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderSimpleMolecule(molecule) {
        const atoms = molecule.atoms;
        const bonds = molecule.bonds;
        
        // 计算分子的边界框
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        atoms.forEach(atom => {
            minX = Math.min(minX, atom.position[0]);
            minY = Math.min(minY, atom.position[1]);
            maxX = Math.max(maxX, atom.position[0]);
            maxY = Math.max(maxY, atom.position[1]);
        });
        
        // 计算缩放和偏移，使分子居中显示
        const padding = 50;
        const scale = Math.min(
            (this.canvas.width - padding * 2) / (maxX - minX || 1),
            (this.canvas.height - padding * 2) / (maxY - minY || 1)
        );
        
        const offsetX = (this.canvas.width - (maxX + minX) * scale) / 2;
        const offsetY = (this.canvas.height - (maxY + minY) * scale) / 2;
        
        // 绘制键
        if (bonds) {
            this.ctx.strokeStyle = '#888888';
            this.ctx.lineWidth = 2;
            
            bonds.forEach(bond => {
                const atom1 = atoms[bond.start];
                const atom2 = atoms[bond.end];
                
                if (atom1 && atom2) {
                    const x1 = atom1.position[0] * scale + offsetX;
                    const y1 = atom1.position[1] * scale + offsetY;
                    const x2 = atom2.position[0] * scale + offsetX;
                    const y2 = atom2.position[1] * scale + offsetY;
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(x1, y1);
                    this.ctx.lineTo(x2, y2);
                    this.ctx.stroke();
                }
            });
        }
        
        // 绘制原子
        atoms.forEach(atom => {
            const x = atom.position[0] * scale + offsetX;
            const y = atom.position[1] * scale + offsetY;
            const radius = this._getAtomRadius(atom.symbol) * scale * 0.5;
            
            // 绘制原子圆形
            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, Math.PI * 2);
            
            // 设置颜色
            const color = this._getColorString(atom.color || this._getAtomColor(atom.symbol));
            this.ctx.fillStyle = color;
            this.ctx.fill();
            
            // 绘制原子符号
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = 'bold 14px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(atom.symbol, x, y);
        });
    }
    
    /**
     * 获取原子半径
     * @private
     * @param {string} symbol - 原子符号
     * @returns {number} 原子半径
     */
    _getAtomRadius(symbol) {
        const radii = {
            'H': 0.5,
            'C': 0.8,
            'N': 0.7,
            'O': 0.7,
            'P': 1.0,
            'S': 1.0,
            'F': 0.7,
            'Cl': 1.0,
            'Br': 1.2,
            'I': 1.4,
            'Na': 1.8,
            'Mg': 1.5,
            'K': 2.2,
            'Ca': 1.8,
            'Fe': 1.4
        };
        
        return radii[symbol] || 1.0;
    }
    
    /**
     * 获取原子颜色
     * @private
     * @param {string} symbol - 原子符号
     * @returns {number} 原子颜色（十六进制）
     */
    _getAtomColor(symbol) {
        const colors = {
            'H': 0xffffff,  // 白色
            'C': 0x808080,  // 灰色
            'N': 0x0000ff,  // 蓝色
            'O': 0xff0000,  // 红色
            'P': 0xffa500,  // 橙色
            'S': 0xffff00,  // 黄色
            'F': 0x90ee90,  // 浅绿色
            'Cl': 0x90ee90, // 浅绿色
            'Br': 0xa52a2a, // 棕色
            'I': 0x800080,  // 紫色
            'Na': 0x0000ff, // 蓝色
            'Mg': 0x00ff00, // 绿色
            'K': 0x0000ff,  // 蓝色
            'Ca': 0x00ff00, // 绿色
            'Fe': 0xffa500  // 橙色
        };
        
        return colors[symbol] || 0xffffff;
    }
    
    /**
     * 将十六进制颜色转换为CSS颜色字符串
     * @private
     * @param {number} hexColor - 十六进制颜色
     * @returns {string} CSS颜色字符串
     */
    _getColorString(hexColor) {
        return '#' + hexColor.toString(16).padStart(6, '0');
    }
    
    /**
     * 清除画布
     * @private
     */
    _clear() {
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    /**
     * 渲染
     * @private
     */
    _render() {
        if (!this.isActive) return;
        
        this._clear();
        
        // 绘制错误消息
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 20px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(this.errorMessage || '备用渲染模式', this.canvas.width / 2, this.canvas.height / 2);
    }
    
    /**
     * 处理窗口大小变化
     * @private
     */
    _onResize() {
        if (!this.isActive) return;
        
        this.canvas.width = this.container.clientWidth;
        this.canvas.height = this.container.clientHeight;
        this._render();
    }
}
