/**
 * 生物学动画系统 - 复杂生物过程演示
 * 包括噬菌体入侵、DNA复制、ATP合酶旋转等
 */

class BiologicalAnimations {
    constructor(viewer) {
        this.viewer = viewer;
        this.isAnimating = false;
        this.currentAnimation = null;
        this.animationGroup = null;
        this.animationSpeed = 1.0;
        
        // 动画定义
        this.animations = {
            bacteriophageInvasion: {
                name: '噬菌体入侵细菌',
                description: '噬菌体附着细菌表面，注射DNA，复制并裂解细菌',
                duration: 15000,
                phases: [
                    { name: '接近', duration: 3000, action: 'approach' },
                    { name: '附着', duration: 2000, action: 'attachment' },
                    { name: 'DNA注射', duration: 3000, action: 'injection' },
                    { name: 'DNA复制', duration: 4000, action: 'replication' },
                    { name: '组装', duration: 2000, action: 'assembly' },
                    { name: '裂解', duration: 1000, action: 'lysis' }
                ]
            },
            atpSynthaseRotation: {
                name: 'ATP合酶旋转',
                description: 'ATP合酶利用质子梯度旋转γ亚基，合成ATP',
                duration: 10000,
                phases: [
                    { name: '质子流入', duration: 2000, action: 'protonFlow' },
                    { name: 'c环旋转', duration: 3000, action: 'cRingRotation' },
                    { name: 'γ亚基旋转', duration: 3000, action: 'gammaRotation' },
                    { name: 'ATP合成', duration: 2000, action: 'atpSynthesis' }
                ]
            },
            dnaReplication: {
                name: 'DNA复制',
                description: 'DNA双螺旋解开，DNA聚合酶合成新链',
                duration: 12000,
                phases: [
                    { name: '解螺旋', duration: 3000, action: 'unwinding' },
                    { name: '引物合成', duration: 2000, action: 'primerSynthesis' },
                    { name: 'DNA聚合', duration: 5000, action: 'polymerization' },
                    { name: '连接', duration: 2000, action: 'ligation' }
                ]
            }
        };
        
        console.log('生物学动画系统已初始化');
    }

    /**
     * 获取可用动画列表
     */
    getAvailableAnimations() {
        return Object.keys(this.animations).map(key => ({
            id: key,
            name: this.animations[key].name,
            description: this.animations[key].description,
            duration: this.animations[key].duration
        }));
    }

    /**
     * 开始生物学动画
     */
    startAnimation(animationId, options = {}) {
        if (this.isAnimating) {
            console.warn('已有动画在进行中');
            return false;
        }

        const animation = this.animations[animationId];
        if (!animation) {
            console.error(`未找到动画: ${animationId}`);
            return false;
        }

        console.log(`开始生物学动画: ${animation.name}`);

        this.currentAnimation = animation;
        this.isAnimating = true;
        this.animationSpeed = options.speed || 1.0;
        this.currentPhaseIndex = 0;
        this.phaseProgress = 0;

        // 清除现有分子
        this.viewer.clearMolecule();

        // 设置动画场景
        this._setupAnimationScene(animationId);

        // 开始动画循环
        this._animateLoop();

        return true;
    }

    /**
     * 停止当前动画
     */
    stopAnimation() {
        if (!this.isAnimating) return;

        console.log('停止生物学动画');
        this.isAnimating = false;
        this.currentAnimation = null;
        this.currentPhaseIndex = 0;
        this.phaseProgress = 0;

        // 清除动画组
        if (this.animationGroup) {
            this.viewer.scene.remove(this.animationGroup);
            this.animationGroup = null;
        }
    }

    /**
     * 设置动画场景
     */
    _setupAnimationScene(animationId) {
        this.animationGroup = new THREE.Group();
        this.viewer.scene.add(this.animationGroup);

        switch (animationId) {
            case 'bacteriophageInvasion':
                this._setupBacteriophageScene();
                break;
            case 'atpSynthaseRotation':
                this._setupATPSynthaseScene();
                break;
            case 'dnaReplication':
                this._setupDNAReplicationScene();
                break;
        }
    }

    /**
     * 设置噬菌体入侵场景
     */
    _setupBacteriophageScene() {
        // 创建细菌细胞 (简化球体)
        const bacteriaGeometry = new THREE.SphereGeometry(3, 32, 32);
        const bacteriaMaterial = new THREE.MeshLambertMaterial({
            color: 0x90EE90,
            transparent: true,
            opacity: 0.7
        });
        this.bacteria = new THREE.Mesh(bacteriaGeometry, bacteriaMaterial);
        this.bacteria.position.set(0, 0, 0);
        this.animationGroup.add(this.bacteria);

        // 创建噬菌体头部
        if (MOLECULES.bacteriophageHead) {
            this.phageHead = this._createMoleculeGroup(MOLECULES.bacteriophageHead);
            this.phageHead.position.set(0, 8, 0);
            this.phageHead.scale.set(0.5, 0.5, 0.5);
            this.animationGroup.add(this.phageHead);
        }

        // 创建噬菌体尾部
        if (MOLECULES.bacteriophageTail) {
            this.phageTail = this._createMoleculeGroup(MOLECULES.bacteriophageTail);
            this.phageTail.position.set(0, 5, 0);
            this.phageTail.scale.set(0.5, 0.5, 0.5);
            this.animationGroup.add(this.phageTail);
        }

        // 创建DNA (初始隐藏)
        if (MOLECULES.dnaDoubleHelix) {
            this.dna = this._createMoleculeGroup(MOLECULES.dnaDoubleHelix);
            this.dna.position.set(0, 6, 0);
            this.dna.scale.set(0.3, 0.3, 0.3);
            this.dna.visible = false;
            this.animationGroup.add(this.dna);
        }

        console.log('噬菌体入侵场景设置完成');
    }

    /**
     * 设置ATP合酶场景
     */
    _setupATPSynthaseScene() {
        if (MOLECULES.atpSynthase) {
            this.atpSynthase = this._createMoleculeGroup(MOLECULES.atpSynthase);
            this.atpSynthase.position.set(0, 0, 0);
            this.animationGroup.add(this.atpSynthase);

            // 分离γ亚基用于旋转动画
            this.gammaSubunit = this.atpSynthase.children[6]; // γ亚基
            if (this.gammaSubunit) {
                this.gammaSubunit.userData.isGamma = true;
            }
        }

        // 创建质子粒子
        this.protons = [];
        for (let i = 0; i < 10; i++) {
            const protonGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const protonMaterial = new THREE.MeshBasicMaterial({ color: 0xff4444 });
            const proton = new THREE.Mesh(protonGeometry, protonMaterial);
            proton.position.set(
                (Math.random() - 0.5) * 6,
                Math.random() * 4 + 2,
                (Math.random() - 0.5) * 6
            );
            this.protons.push(proton);
            this.animationGroup.add(proton);
        }

        console.log('ATP合酶场景设置完成');
    }

    /**
     * 设置DNA复制场景
     */
    _setupDNAReplicationScene() {
        if (MOLECULES.dnaDoubleHelix) {
            this.dnaOriginal = this._createMoleculeGroup(MOLECULES.dnaDoubleHelix);
            this.dnaOriginal.position.set(0, 0, 0);
            this.animationGroup.add(this.dnaOriginal);

            // 创建新合成的DNA链 (初始隐藏)
            this.dnaNew = this._createMoleculeGroup(MOLECULES.dnaDoubleHelix);
            this.dnaNew.position.set(3, 0, 0);
            this.dnaNew.visible = false;
            this.animationGroup.add(this.dnaNew);
        }

        console.log('DNA复制场景设置完成');
    }

    /**
     * 动画循环
     */
    _animateLoop() {
        if (!this.isAnimating || !this.currentAnimation) return;

        const currentPhase = this.currentAnimation.phases[this.currentPhaseIndex];
        if (!currentPhase) {
            this._completeAnimation();
            return;
        }

        // 执行当前阶段的动画
        this._executePhaseAnimation(currentPhase);

        // 更新进度
        this.phaseProgress += 16 / (currentPhase.duration / this.animationSpeed);

        if (this.phaseProgress >= 1.0) {
            this.currentPhaseIndex++;
            this.phaseProgress = 0;
        }

        requestAnimationFrame(() => this._animateLoop());
    }

    /**
     * 执行阶段动画
     */
    _executePhaseAnimation(phase) {
        switch (phase.action) {
            case 'approach':
                this._animateApproach();
                break;
            case 'attachment':
                this._animateAttachment();
                break;
            case 'injection':
                this._animateInjection();
                break;
            case 'replication':
                this._animateReplication();
                break;
            case 'assembly':
                this._animateAssembly();
                break;
            case 'lysis':
                this._animateLysis();
                break;
            case 'protonFlow':
                this._animateProtonFlow();
                break;
            case 'cRingRotation':
                this._animateCRingRotation();
                break;
            case 'gammaRotation':
                this._animateGammaRotation();
                break;
            case 'atpSynthesis':
                this._animateATPSynthesis();
                break;
            case 'unwinding':
                this._animateUnwinding();
                break;
            case 'primerSynthesis':
                this._animatePrimerSynthesis();
                break;
            case 'polymerization':
                this._animatePolymerization();
                break;
            case 'ligation':
                this._animateLigation();
                break;
        }
    }

    /**
     * 噬菌体接近动画
     */
    _animateApproach() {
        if (this.phageHead && this.phageTail) {
            const targetY = 4;
            this.phageHead.position.y += (targetY - this.phageHead.position.y) * 0.02;
            this.phageTail.position.y += ((targetY - 2) - this.phageTail.position.y) * 0.02;
            
            // 轻微摆动
            const time = Date.now() * 0.003;
            this.phageHead.rotation.z = Math.sin(time) * 0.1;
            this.phageTail.rotation.z = Math.sin(time) * 0.1;
        }
    }

    /**
     * 噬菌体附着动画
     */
    _animateAttachment() {
        if (this.phageTail) {
            // 尾纤维伸展
            this.phageTail.scale.y += 0.01;
            this.phageTail.position.y -= 0.02;
        }
    }

    /**
     * DNA注射动画
     */
    _animateInjection() {
        if (this.dna) {
            this.dna.visible = true;
            this.dna.position.y -= 0.05;
            this.dna.scale.x += 0.01;
            this.dna.scale.z += 0.01;
        }
    }

    /**
     * 创建分子组 (复用ReactionEngine的方法)
     */
    _createMoleculeGroup(molecule) {
        const group = new THREE.Group();

        // 渲染原子
        molecule.atoms.forEach((atom, index) => {
            const radius = this._getAtomRadius(atom.symbol) * 0.5;
            const geometry = new THREE.SphereGeometry(radius, 16, 16);
            const material = new THREE.MeshLambertMaterial({
                color: this._getAtomColor(atom.symbol)
            });

            const atomMesh = new THREE.Mesh(geometry, material);
            atomMesh.position.set(atom.position[0], atom.position[1], atom.position[2]);
            group.add(atomMesh);
        });

        // 渲染化学键
        if (molecule.bonds) {
            molecule.bonds.forEach((bond) => {
                const atom1 = molecule.atoms[bond.atoms[0]];
                const atom2 = molecule.atoms[bond.atoms[1]];

                if (atom1 && atom2) {
                    const bondMesh = this._createBond(atom1.position, atom2.position);
                    group.add(bondMesh);
                }
            });
        }

        return group;
    }

    /**
     * 创建化学键
     */
    _createBond(pos1, pos2) {
        const start = new THREE.Vector3(pos1[0], pos1[1], pos1[2]);
        const end = new THREE.Vector3(pos2[0], pos2[1], pos2[2]);
        const length = start.distanceTo(end);

        const geometry = new THREE.CylinderGeometry(0.1, 0.1, length, 8);
        const material = new THREE.MeshLambertMaterial({ color: 0x888888 });
        const bond = new THREE.Mesh(geometry, material);

        bond.position.copy(start).add(end).multiplyScalar(0.5);
        bond.lookAt(end);
        bond.rotateX(Math.PI / 2);

        return bond;
    }

    /**
     * 获取原子半径
     */
    _getAtomRadius(symbol) {
        const radii = {
            'H': 0.31, 'C': 0.76, 'N': 0.71, 'O': 0.66,
            'F': 0.57, 'P': 1.07, 'S': 1.05, 'Cl': 0.99
        };
        return radii[symbol] || 1.0;
    }

    /**
     * 获取原子颜色
     */
    _getAtomColor(symbol) {
        const colors = {
            'H': 0xffffff, 'C': 0x909090, 'N': 0x3050f8,
            'O': 0xff0d0d, 'P': 0xff8000, 'S': 0xffff30
        };
        return colors[symbol] || 0xff69b4;
    }

    /**
     * 完成动画
     */
    _completeAnimation() {
        console.log(`生物学动画完成: ${this.currentAnimation.name}`);
        this.isAnimating = false;
    }

    // 其他动画方法的占位符
    _animateReplication() { /* DNA复制动画 */ }
    _animateAssembly() { /* 病毒组装动画 */ }
    _animateLysis() { /* 细胞裂解动画 */ }
    _animateProtonFlow() { /* 质子流动动画 */ }
    _animateCRingRotation() { /* c环旋转动画 */ }
    _animateGammaRotation() { /* γ亚基旋转动画 */ }
    _animateATPSynthesis() { /* ATP合成动画 */ }
    _animateUnwinding() { /* DNA解螺旋动画 */ }
    _animatePrimerSynthesis() { /* 引物合成动画 */ }
    _animatePolymerization() { /* DNA聚合动画 */ }
    _animateLigation() { /* DNA连接动画 */ }
}
