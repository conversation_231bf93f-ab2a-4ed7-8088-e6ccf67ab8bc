/* 全局样式 - 增强版 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --accent-color: #e74c3c;
    --dark-bg: #2c3e50;
    --light-bg: #f5f5f5;
    --text-color: #333;
    --light-text: #fff;
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Segoe UI', 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    overflow-x: hidden;
    position: relative;
}

/* 添加动态背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundPulse 10s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

header {
    background: linear-gradient(135deg, #2c3e50, #1a2530);
    color: var(--light-text);
    text-align: center;
    padding: 1.5rem 0;
    box-shadow: var(--box-shadow);
    position: relative;
}

header h1 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

header .subtitle {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 300;
    letter-spacing: 1px;
}

footer {
    background: linear-gradient(135deg, #2c3e50, #1a2530);
    color: var(--light-text);
    text-align: center;
    padding: 1rem 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

footer p {
    margin: 0 auto;
}

.footer-links {
    position: absolute;
    right: 20px;
}

.footer-links a {
    color: var(--light-text);
    margin-left: 15px;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-links a:hover {
    opacity: 1;
    text-decoration: underline;
}

.container {
    display: flex;
    height: calc(100vh - 140px);
    padding: 20px;
    max-width: 1800px;
    margin: 0 auto;
}

/* 控制面板样式 */
.control-panel {
    width: 320px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
    transition: all 0.3s ease;
    max-height: calc(100vh - 160px);
}

.control-panel h2 {
    color: var(--dark-bg);
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.control-group {
    margin-bottom: 25px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-bg);
    font-size: 0.95rem;
}

select, input[type="text"], input[type="number"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 0.95rem;
    transition: all 0.3s;
    color: var(--text-color);
}

/* 输入框和按钮组合 */
.input-with-button {
    display: flex;
    gap: 8px;
}

.input-with-button input {
    flex: 3;
}

.input-with-button button {
    flex: 1;
}

.help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

select:focus, input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* 美化下拉菜单 */
select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0l6 6 6-6z" fill="%23666"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 30px;
}

optgroup {
    font-weight: bold;
    color: var(--dark-bg);
}

/* 按钮样式 */
.button-row {
    display: flex;
    gap: 10px;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    font-size: 0.95rem;
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
    flex: 2;
}

.primary-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.secondary-button {
    background-color: #f1f1f1;
    color: var(--text-color);
    flex: 1;
}

.secondary-button:hover {
    background-color: #e1e1e1;
    transform: translateY(-2px);
}

/* 滑块样式 */
input[type="range"] {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    margin: 10px 0;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    transition: all 0.2s;
}

input[type="range"]::-webkit-slider-thumb:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* 开关样式 */
.toggle-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5px;
}

.toggle-container span {
    font-size: 0.85rem;
    color: #666;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 0 10px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.info-panel {
    margin-top: 25px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.info-panel h3 {
    color: var(--dark-bg);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.info-panel p {
    font-size: 0.9rem;
    line-height: 1.5;
}

.info-panel .error {
    color: #e74c3c;
    font-weight: bold;
}

.info-panel a {
    color: var(--primary-color);
    text-decoration: none;
}

.info-panel a:hover {
    text-decoration: underline;
}

/* 可视化区域样式 */
.viewer-container {
    flex: 1;
    margin-left: 25px;
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

#molecule-viewer {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, #1a1a2e 0%, #0f0f23 100%);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

/* 添加可视化区域的装饰效果 */
#molecule-viewer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(155, 89, 182, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* 对话框样式 */
.dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.dialog-content {
    background-color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: color 0.3s;
}

.close-button:hover {
    color: var(--accent-color);
}

.dialog h2 {
    color: var(--dark-bg);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

.dialog h3 {
    color: var(--dark-bg);
    margin: 20px 0 10px;
    font-size: 1.1rem;
}

.dialog p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.dialog ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.dialog li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.dialog.active {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.dialog.active .dialog-content {
    animation: slideIn 0.3s ease-out;
}

/* 高级功能面板样式 */
.advanced-features {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #dee2e6;
    margin-top: 15px;
}

.advanced-features h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid #6c757d;
    padding-bottom: 8px;
}

.feature-group {
    margin-bottom: 15px;
}

.button-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 8px;
}

.feature-button {
    padding: 8px 12px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.feature-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.feature-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* 教育模式样式 */
.education-mode {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #c3e6cb;
    margin-top: 15px;
}

.education-mode h3 {
    color: #155724;
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid #28a745;
    padding-bottom: 8px;
}

.education-selector {
    margin-bottom: 15px;
}

.learning-tools {
    margin-bottom: 15px;
}

.learning-tools .button-grid .feature-button {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.learning-tools .button-grid .feature-button:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.progress-tracking {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 化学反应控制样式增强 */
.reaction-controls {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #ffeaa7;
    margin-top: 15px;
}

.reaction-controls h3 {
    color: #856404;
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid #ffc107;
    padding-bottom: 8px;
}

.reaction-controls-buttons button {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    margin: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.reaction-controls-buttons button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.4);
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
}

.reaction-controls-buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

/* 生物学动画控制样式增强 */
.biological-animations {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #b3d9ff;
    margin-top: 15px;
}

.biological-animations h3 {
    color: #004085;
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.animation-controls-buttons button {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    margin: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.animation-controls-buttons button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.animation-controls-buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .container {
        flex-direction: column;
        height: auto;
        padding: 15px;
    }

    .control-panel {
        width: 100%;
        margin-bottom: 20px;
        max-height: 50vh;
    }

    .viewer-container {
        margin-left: 0;
        height: 60vh;
        margin-bottom: 60px;
    }
}

@media (max-width: 576px) {
    header h1 {
        font-size: 1.8rem;
    }

    header .subtitle {
        font-size: 0.9rem;
    }

    .control-panel {
        padding: 15px;
    }

    .button-row {
        flex-direction: column;
        gap: 8px;
    }

    .primary-button, .secondary-button {
        width: 100%;
    }

    .viewer-container {
        height: 50vh;
    }

    .dialog-content {
        padding: 15px;
        width: 95%;
    }
}

/* 化学反应控制样式 */
.reaction-controls {
    border: 2px solid transparent;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(155, 89, 182, 0.2));
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.reaction-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #3498db, #9b59b6);
    border-radius: 15px;
    padding: 2px;
    z-index: -1;
}

.reaction-controls::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 13px;
    z-index: -1;
}

.reaction-controls h3 {
    margin: 0 0 15px 0;
    color: var(--accent-color);
    font-size: 1.2em;
    text-align: center;
}

.reaction-selector {
    margin-bottom: 15px;
}

.reaction-info {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
    border-left: 4px solid var(--accent-color);
    font-size: 0.9em;
    line-height: 1.4;
}

.reaction-controls-buttons {
    display: flex;
    gap: 8px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.reaction-controls-buttons button {
    flex: 1;
    min-width: 80px;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s ease;
}

#start-reaction {
    background-color: var(--success-color);
    color: white;
}

#start-reaction:hover:not(:disabled) {
    background-color: #27ae60;
    transform: translateY(-2px);
}

#start-reaction:disabled {
    background-color: #7f8c8d;
    cursor: not-allowed;
}

#stop-reaction {
    background-color: var(--danger-color);
    color: white;
}

#stop-reaction:hover:not(:disabled) {
    background-color: #c0392b;
    transform: translateY(-2px);
}

#stop-reaction:disabled {
    background-color: #7f8c8d;
    cursor: not-allowed;
}

#reset-reaction {
    background-color: var(--secondary-color);
    color: white;
}

#reset-reaction:hover {
    background-color: #5a6c7d;
    transform: translateY(-2px);
}

.reaction-settings {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.reaction-settings label {
    font-size: 0.9em;
    min-width: 80px;
}

#reaction-speed {
    flex: 1;
    min-width: 100px;
}

#reaction-speed-value {
    font-weight: bold;
    color: var(--accent-color);
    min-width: 40px;
    text-align: center;
}

.reaction-progress {
    margin: 10px 0;
}

.reaction-progress label {
    display: block;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

#reaction-progress-text {
    font-size: 0.8em;
    color: var(--text-secondary);
    margin-top: 5px;
    display: block;
}

/* 反应动画效果 */
.reaction-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: var(--accent-color);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-50px);
    }
}
