/**
 * 分子加载器类
 * 负责加载和解析分子数据
 */
class MoleculeLoader {
    constructor() {
        console.log('MoleculeLoader构造函数被调用');

        // 检查MOLECULES是否已定义，如果未定义则创建一个空对象
        if (typeof MOLECULES === 'undefined') {
            console.error('MOLECULES对象未定义，创建空对象作为替代');
            this.molecules = {
                test: {
                    name: "备用测试分子",
                    formula: "Test",
                    description: "这是一个备用测试分子，在MOLECULES未定义时使用。",
                    atoms: [
                        { symbol: "X", position: [0, 0, 0], color: 0xff00ff }
                    ],
                    bonds: [],
                    animation: {
                        type: "vibration",
                        frequency: 0.05,
                        amplitude: 0.1
                    }
                }
            };
        } else {
            console.log('MOLECULES对象已定义，包含以下分子:', Object.keys(MOLECULES));
            this.molecules = MOLECULES;
        }
    }

    /**
     * 获取分子数据
     * @param {string} moleculeId - 分子的ID
     * @returns {Object} 分子数据对象
     */
    getMolecule(moleculeId) {
        console.log(`尝试获取分子: ${moleculeId}`);
        console.log(`可用分子: ${Object.keys(this.molecules).join(', ')}`);

        if (!this.molecules[moleculeId]) {
            console.error(`分子 ${moleculeId} 不存在`);

            // 如果请求的分子不存在，返回第一个可用的分子
            const availableMolecules = Object.keys(this.molecules);
            if (availableMolecules.length > 0) {
                const firstMolecule = availableMolecules[0];
                console.log(`返回第一个可用分子: ${firstMolecule}`);
                return this.molecules[firstMolecule];
            }

            return null;
        }

        console.log(`成功获取分子: ${moleculeId}`);
        return this.molecules[moleculeId];
    }

    /**
     * 获取所有可用分子的ID列表
     * @returns {Array} 分子ID数组
     */
    getAvailableMolecules() {
        return Object.keys(this.molecules);
    }

    /**
     * 获取分子信息的HTML
     * @param {string} moleculeId - 分子的ID
     * @returns {string} 包含分子信息的HTML
     */
    getMoleculeInfoHTML(moleculeId) {
        const molecule = this.getMolecule(moleculeId);
        if (!molecule) return '<p>无法加载分子信息</p>';

        // 计算分子的化学式（如果没有提供）
        let formula = molecule.formula;
        if (!formula) {
            formula = this._calculateFormula(molecule.atoms);
        }

        // 获取分子描述（如果没有提供）
        let description = molecule.description;
        if (!description) {
            description = `这是一个${molecule.name}，包含${molecule.atoms.length}个原子和${molecule.bonds.length}个键。`;
        }

        return `
            <h4>${molecule.name}</h4>
            <p><strong>分类:</strong> ${molecule.category || '未分类'}</p>
            <p><strong>化学式:</strong> ${formula}</p>
            <p><strong>原子数:</strong> ${molecule.atoms.length}</p>
            <p><strong>键数:</strong> ${molecule.bonds.length}</p>
            <p>${description}</p>
        `;
    }

    /**
     * 加载PDB格式的分子数据
     * @param {string} url - PDB文件的URL或PDB ID
     * @param {Object} options - 加载选项
     * @returns {Promise} 包含分子数据的Promise
     */
    loadPDB(url, options = {}) {
        return new Promise((resolve, reject) => {
            // 检查是否是PDB ID (通常是4个字符的代码)
            if (/^[a-zA-Z0-9]{4}$/.test(url)) {
                // 如果是PDB ID，构建完整URL
                const pdbId = url.toLowerCase();
                url = `https://files.rcsb.org/download/${pdbId}.pdb`;
                console.log(`检测到PDB ID: ${pdbId}，使用URL: ${url}`);
            }

            try {
                const loader = new THREE.PDBLoader();
                loader.load(url,
                    (pdb) => {
                        console.log('PDB数据加载成功:', pdb);
                        // 转换PDB数据为我们的分子格式
                        const molecule = this._convertPDBToMolecule(pdb, options);
                        resolve(molecule);
                    },
                    (xhr) => {
                        if (xhr.lengthComputable) {
                            const percentComplete = Math.round(xhr.loaded / xhr.total * 100);
                            console.log(`PDB加载进度: ${percentComplete}%`);
                        }
                    },
                    (error) => {
                        console.error('加载PDB文件时出错:', error);
                        reject(error);
                    }
                );
            } catch (error) {
                console.error('初始化PDB加载器时出错:', error);
                reject(error);
            }
        });
    }

    /**
     * 将PDB数据转换为我们的分子格式
     * @private
     * @param {Object} pdb - PDB加载器返回的数据
     * @param {Object} options - 转换选项
     * @returns {Object} 我们格式的分子数据
     */
    _convertPDBToMolecule(pdb, options = {}) {
        console.log('开始转换PDB数据到分子格式');

        // 提取分子名称和描述
        const name = options.name || pdb.name || "PDB分子";
        const description = options.description || `这是从PDB数据库加载的分子结构，包含${pdb.geometryAtoms.attributes.position.count}个原子。`;

        // 创建原子数组
        const atoms = [];
        const positions = pdb.geometryAtoms.attributes.position.array;
        const colors = pdb.geometryAtoms.attributes.color.array;
        const atomTypes = pdb.json.atoms;

        for (let i = 0; i < positions.length; i += 3) {
            const index = i / 3;
            const atomType = atomTypes[index] || {};

            // 获取原子符号，如果不可用则使用元素名称或默认为"X"
            let symbol = atomType.element || "X";

            // 标准化符号 (确保首字母大写，其余小写)
            if (symbol.length > 0) {
                symbol = symbol.charAt(0).toUpperCase() + symbol.slice(1).toLowerCase();
            }

            // 创建原子对象
            atoms.push({
                symbol: symbol,
                position: [
                    positions[i],
                    positions[i + 1],
                    positions[i + 2]
                ],
                color: this._rgbToHex(
                    Math.floor(colors[i] * 255),
                    Math.floor(colors[i + 1] * 255),
                    Math.floor(colors[i + 2] * 255)
                ),
                // 添加额外的原子信息
                residue: atomType.residue,
                chainId: atomType.chainId,
                serial: atomType.serial
            });
        }

        // 创建键数组
        const bonds = [];
        const bondPositions = pdb.geometryBonds.attributes.position.array;

        // PDBLoader中，键是通过两个点的位置定义的
        // 我们需要找到这些点对应的原子索引
        for (let i = 0; i < bondPositions.length; i += 6) {
            // 键的起点和终点
            const startPos = [
                bondPositions[i],
                bondPositions[i + 1],
                bondPositions[i + 2]
            ];

            const endPos = [
                bondPositions[i + 3],
                bondPositions[i + 4],
                bondPositions[i + 5]
            ];

            // 找到最接近这些位置的原子
            const startIndex = this._findClosestAtomIndex(atoms, startPos);
            const endIndex = this._findClosestAtomIndex(atoms, endPos);

            if (startIndex !== -1 && endIndex !== -1 && startIndex !== endIndex) {
                bonds.push({
                    start: startIndex,
                    end: endIndex
                });
            }
        }

        // 计算分子的化学式
        const formula = this._calculateFormula(atoms);

        // 创建分子对象
        const molecule = {
            name: name,
            formula: formula,
            description: description,
            atoms: atoms,
            bonds: bonds,
            // 添加默认动画
            animation: {
                type: "vibration",
                frequency: 0.03,
                amplitude: 0.08,
                speed: 1.0
            },
            // 添加PDB特定信息
            pdbInfo: {
                id: options.pdbId || "unknown",
                title: pdb.json.title || "",
                authors: pdb.json.authors || "",
                journal: pdb.json.journal || "",
                url: options.url || ""
            }
        };

        console.log(`PDB转换完成，生成了包含${atoms.length}个原子和${bonds.length}个键的分子`);
        return molecule;
    }

    /**
     * 找到最接近给定位置的原子索引
     * @private
     * @param {Array} atoms - 原子数组
     * @param {Array} position - 位置坐标 [x, y, z]
     * @returns {number} 最接近的原子索引，如果没有找到则返回-1
     */
    _findClosestAtomIndex(atoms, position) {
        let closestIndex = -1;
        let minDistance = Infinity;

        for (let i = 0; i < atoms.length; i++) {
            const atom = atoms[i];
            const distance = Math.sqrt(
                Math.pow(atom.position[0] - position[0], 2) +
                Math.pow(atom.position[1] - position[1], 2) +
                Math.pow(atom.position[2] - position[2], 2)
            );

            if (distance < minDistance) {
                minDistance = distance;
                closestIndex = i;
            }
        }

        // 设置一个阈值，如果最小距离太大，则认为没有找到匹配的原子
        return minDistance < 1.0 ? closestIndex : -1;
    }

    /**
     * 将RGB颜色转换为十六进制颜色值
     * @private
     * @param {number} r - 红色分量 (0-255)
     * @param {number} g - 绿色分量 (0-255)
     * @param {number} b - 蓝色分量 (0-255)
     * @returns {number} 十六进制颜色值
     */
    _rgbToHex(r, g, b) {
        return (r << 16) | (g << 8) | b;
    }

    /**
     * 根据原子数组计算分子的化学式
     * @private
     * @param {Array} atoms - 原子数组
     * @returns {string} 分子的化学式
     */
    _calculateFormula(atoms) {
        // 统计各元素的数量
        const elementCounts = {};

        atoms.forEach(atom => {
            const symbol = atom.symbol;
            if (symbol && symbol !== 'X') {
                elementCounts[symbol] = (elementCounts[symbol] || 0) + 1;
            }
        });

        // 按元素符号排序
        const sortedElements = Object.keys(elementCounts).sort();

        // 构建化学式
        let formula = '';
        sortedElements.forEach(element => {
            formula += element;
            if (elementCounts[element] > 1) {
                // 将数字转换为下标形式
                const subscript = elementCounts[element].toString()
                    .replace(/0/g, '₀')
                    .replace(/1/g, '₁')
                    .replace(/2/g, '₂')
                    .replace(/3/g, '₃')
                    .replace(/4/g, '₄')
                    .replace(/5/g, '₅')
                    .replace(/6/g, '₆')
                    .replace(/7/g, '₇')
                    .replace(/8/g, '₈')
                    .replace(/9/g, '₉');
                formula += subscript;
            }
        });

        return formula;
    }
}
