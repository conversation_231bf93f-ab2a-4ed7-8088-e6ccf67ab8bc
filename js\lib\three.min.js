// Three.js r132 - https://github.com/mrdoob/three.js
// 注意：这是一个简化版的Three.js库，仅包含基本功能
// 实际使用时，请下载完整的Three.js库
// 已优化：优先使用CDN加载完整版本

var THREE = { 
    REVISION: '132',
    // 添加基本的WebGL检测
    WebGLRenderer: function(parameters) {
        console.warn('使用简化版WebGLRenderer，建议使用CDN版本');
        return {
            domElement: document.createElement('canvas'),
            setSize: function() {},
            render: function() {},
            setClearColor: function() {},
            setPixelRatio: function() {}
        };
    },
    Scene: function() {
        return {
            add: function() {},
            remove: function() {}
        };
    },
    PerspectiveCamera: function() {
        return {
            position: { set: function() {} },
            lookAt: function() {}
        };
    }
};

// 基本几何体
THREE.BoxGeometry = function(width, height, depth) {
    this.type = 'BoxGeometry';
    this.parameters = {
        width: width,
        height: height,
        depth: depth
    };
};

// 球体几何体
THREE.SphereGeometry = function(radius, widthSegments, heightSegments) {
    this.type = 'SphereGeometry';
    this.parameters = {
        radius: radius,
        widthSegments: widthSegments,
        heightSegments: heightSegments
    };
};

// 圆柱体几何体
THREE.CylinderGeometry = function(radiusTop, radiusBottom, height, radialSegments) {
    this.type = 'CylinderGeometry';
    this.parameters = {
        radiusTop: radiusTop,
        radiusBottom: radiusBottom,
        height: height,
        radialSegments: radialSegments
    };
};

// 基本材质
THREE.MeshBasicMaterial = function(parameters) {
    this.type = 'MeshBasicMaterial';
    this.color = new THREE.Color(0xffffff);
    this.transparent = false;
    this.opacity = 1.0;
    
    if (parameters) {
        if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
        if (parameters.transparent !== undefined) this.transparent = parameters.transparent;
        if (parameters.opacity !== undefined) this.opacity = parameters.opacity;
    }
};

// 标准材质
THREE.MeshStandardMaterial = function(parameters) {
    this.type = 'MeshStandardMaterial';
    this.color = new THREE.Color(0xffffff);
    this.roughness = 0.5;
    this.metalness = 0.5;
    this.emissive = new THREE.Color(0x000000);
    this.emissiveIntensity = 1.0;
    this.transparent = false;
    this.opacity = 1.0;
    
    if (parameters) {
        if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
        if (parameters.roughness !== undefined) this.roughness = parameters.roughness;
        if (parameters.metalness !== undefined) this.metalness = parameters.metalness;
        if (parameters.emissive !== undefined) this.emissive = new THREE.Color(parameters.emissive);
        if (parameters.emissiveIntensity !== undefined) this.emissiveIntensity = parameters.emissiveIntensity;
        if (parameters.transparent !== undefined) this.transparent = parameters.transparent;
        if (parameters.opacity !== undefined) this.opacity = parameters.opacity;
    }
};

// Phong材质
THREE.MeshPhongMaterial = function(parameters) {
    this.type = 'MeshPhongMaterial';
    this.color = new THREE.Color(0xffffff);
    this.specular = new THREE.Color(0x111111);
    this.shininess = 30;
    this.transparent = false;
    this.opacity = 1.0;
    
    if (parameters) {
        if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
        if (parameters.specular !== undefined) this.specular = new THREE.Color(parameters.specular);
        if (parameters.shininess !== undefined) this.shininess = parameters.shininess;
        if (parameters.transparent !== undefined) this.transparent = parameters.transparent;
        if (parameters.opacity !== undefined) this.opacity = parameters.opacity;
    }
};

// 线条材质
THREE.LineBasicMaterial = function(parameters) {
    this.type = 'LineBasicMaterial';
    this.color = new THREE.Color(0xffffff);
    this.linewidth = 1;
    
    if (parameters) {
        if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
        if (parameters.linewidth !== undefined) this.linewidth = parameters.linewidth;
    }
};

// 颜色
THREE.Color = function(color) {
    this.r = 1;
    this.g = 1;
    this.b = 1;
    
    if (color !== undefined) {
        this.set(color);
    }
};

THREE.Color.prototype = {
    constructor: THREE.Color,
    
    set: function(color) {
        if (typeof color === 'number') {
            this.setHex(color);
        }
        return this;
    },
    
    setHex: function(hex) {
        hex = Math.floor(hex);
        this.r = (hex >> 16 & 255) / 255;
        this.g = (hex >> 8 & 255) / 255;
        this.b = (hex & 255) / 255;
        return this;
    }
};

// 向量
THREE.Vector2 = function(x, y) {
    this.x = x || 0;
    this.y = y || 0;
};

THREE.Vector3 = function(x, y, z) {
    this.x = x || 0;
    this.y = y || 0;
    this.z = z || 0;
};

THREE.Vector3.prototype = {
    constructor: THREE.Vector3,
    
    set: function(x, y, z) {
        this.x = x;
        this.y = y;
        this.z = z;
        return this;
    },
    
    copy: function(v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    },
    
    add: function(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    },
    
    sub: function(v) {
        this.x -= v.x;
        this.y -= v.y;
        this.z -= v.z;
        return this;
    },
    
    multiplyScalar: function(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    },
    
    normalize: function() {
        return this.divideScalar(this.length() || 1);
    },
    
    length: function() {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
    },
    
    divideScalar: function(scalar) {
        return this.multiplyScalar(1 / scalar);
    },
    
    clone: function() {
        return new THREE.Vector3(this.x, this.y, this.z);
    }
};

// 场景
THREE.Scene = function() {
    this.type = 'Scene';
    this.children = [];
    this.background = null;
    this.fog = null;
};

THREE.Scene.prototype = {
    constructor: THREE.Scene,
    
    add: function(object) {
        this.children.push(object);
        return this;
    },
    
    remove: function(object) {
        const index = this.children.indexOf(object);
        if (index !== -1) {
            this.children.splice(index, 1);
        }
        return this;
    }
};

// 相机
THREE.PerspectiveCamera = function(fov, aspect, near, far) {
    this.type = 'PerspectiveCamera';
    this.fov = fov !== undefined ? fov : 50;
    this.aspect = aspect !== undefined ? aspect : 1;
    this.near = near !== undefined ? near : 0.1;
    this.far = far !== undefined ? far : 2000;
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
};

THREE.PerspectiveCamera.prototype = {
    constructor: THREE.PerspectiveCamera,
    
    lookAt: function(x, y, z) {
        // 简化版，实际实现更复杂
        if (x.isVector3) {
            this.target = x.clone();
        } else {
            this.target = new THREE.Vector3(x, y, z);
        }
        return this;
    },
    
    updateProjectionMatrix: function() {
        // 简化版，实际实现更复杂
        return this;
    }
};

// 渲染器
THREE.WebGLRenderer = function(parameters) {
    this.domElement = document.createElement('canvas');
    this.context = this.domElement.getContext('webgl') || this.domElement.getContext('experimental-webgl');
    
    if (parameters) {
        if (parameters.canvas !== undefined) {
            this.domElement = parameters.canvas;
        }
    }
};

THREE.WebGLRenderer.prototype = {
    constructor: THREE.WebGLRenderer,
    
    setSize: function(width, height) {
        this.domElement.width = width;
        this.domElement.height = height;
        return this;
    },
    
    setPixelRatio: function(value) {
        this.pixelRatio = value;
        return this;
    },
    
    render: function(scene, camera) {
        // 简化版，实际实现更复杂
        return this;
    }
};

// 网格
THREE.Mesh = function(geometry, material) {
    this.type = 'Mesh';
    this.geometry = geometry;
    this.material = material;
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
    this.scale = new THREE.Vector3(1, 1, 1);
    this.userData = {};
};

// 线段
THREE.LineSegments = function(geometry, material) {
    this.type = 'LineSegments';
    this.geometry = geometry;
    this.material = material;
    this.position = new THREE.Vector3();
};

// 缓冲几何体
THREE.BufferGeometry = function() {
    this.type = 'BufferGeometry';
    this.attributes = {};
};

THREE.BufferGeometry.prototype = {
    constructor: THREE.BufferGeometry,
    
    setAttribute: function(name, attribute) {
        this.attributes[name] = attribute;
        return this;
    }
};

// 灯光
THREE.AmbientLight = function(color, intensity) {
    this.type = 'AmbientLight';
    this.color = new THREE.Color(color);
    this.intensity = intensity !== undefined ? intensity : 1;
};

THREE.DirectionalLight = function(color, intensity) {
    this.type = 'DirectionalLight';
    this.color = new THREE.Color(color);
    this.intensity = intensity !== undefined ? intensity : 1;
    this.position = new THREE.Vector3(0, 1, 0);
    this.castShadow = false;
    this.shadow = {
        mapSize: { width: 512, height: 512 },
        camera: { near: 0.5, far: 500, left: -100, right: 100, top: 100, bottom: -100 },
        bias: 0
    };
};

THREE.PointLight = function(color, intensity, distance, decay) {
    this.type = 'PointLight';
    this.color = new THREE.Color(color);
    this.intensity = intensity !== undefined ? intensity : 1;
    this.distance = distance !== undefined ? distance : 0;
    this.decay = decay !== undefined ? decay : 1;
    this.position = new THREE.Vector3();
};

THREE.HemisphereLight = function(skyColor, groundColor, intensity) {
    this.type = 'HemisphereLight';
    this.color = new THREE.Color(skyColor);
    this.groundColor = new THREE.Color(groundColor);
    this.intensity = intensity !== undefined ? intensity : 1;
};

// 雾
THREE.FogExp2 = function(color, density) {
    this.color = new THREE.Color(color);
    this.density = density !== undefined ? density : 0.00025;
};

// 纹理
THREE.Texture = function(image) {
    this.image = image;
};

THREE.CanvasTexture = function(canvas) {
    THREE.Texture.call(this, canvas);
    this.type = 'CanvasTexture';
};

THREE.CanvasTexture.prototype = Object.create(THREE.Texture.prototype);
THREE.CanvasTexture.prototype.constructor = THREE.CanvasTexture;

// 轨道控制器
THREE.OrbitControls = function(camera, domElement) {
    this.camera = camera;
    this.domElement = domElement;
    this.enabled = true;
    this.target = new THREE.Vector3();
    this.enableDamping = false;
    this.dampingFactor = 0.05;
    this.enableZoom = true;
    this.zoomSpeed = 1.0;
    this.enableRotate = true;
    this.rotateSpeed = 1.0;
    this.enablePan = true;
    this.panSpeed = 1.0;
    this.minDistance = 0;
    this.maxDistance = Infinity;
    this.minPolarAngle = 0;
    this.maxPolarAngle = Math.PI;
    this.autoRotate = false;
    this.autoRotateSpeed = 2.0;
};

THREE.OrbitControls.prototype = {
    constructor: THREE.OrbitControls,
    
    update: function() {
        // 简化版，实际实现更复杂
        return true;
    }
};

// 时钟
THREE.Clock = function(autoStart) {
    this.autoStart = autoStart !== undefined ? autoStart : true;
    this.startTime = 0;
    this.oldTime = 0;
    this.elapsedTime = 0;
    this.running = false;
    
    if (this.autoStart) {
        this.start();
    }
};

THREE.Clock.prototype = {
    constructor: THREE.Clock,
    
    start: function() {
        this.startTime = Date.now();
        this.oldTime = this.startTime;
        this.elapsedTime = 0;
        this.running = true;
    },
    
    getDelta: function() {
        let diff = 0;
        
        if (this.running) {
            const newTime = Date.now();
            diff = (newTime - this.oldTime) / 1000;
            this.oldTime = newTime;
            this.elapsedTime += diff;
        }
        
        return diff;
    }
};

// 辅助工具
THREE.Float32BufferAttribute = function(array, itemSize) {
    this.array = array;
    this.itemSize = itemSize;
    this.count = array.length / itemSize;
};

// 四元数
THREE.Quaternion = function(x, y, z, w) {
    this._x = x || 0;
    this._y = y || 0;
    this._z = z || 0;
    this._w = w !== undefined ? w : 1;
};

THREE.Quaternion.prototype = {
    constructor: THREE.Quaternion,
    
    setFromUnitVectors: function(vFrom, vTo) {
        // 简化版，实际实现更复杂
        return this;
    }
};

// 后期处理
THREE.EffectComposer = function(renderer) {
    this.renderer = renderer;
    this.passes = [];
};

THREE.EffectComposer.prototype = {
    constructor: THREE.EffectComposer,
    
    addPass: function(pass) {
        this.passes.push(pass);
    },
    
    render: function() {
        // 简化版，实际实现更复杂
    },
    
    setSize: function(width, height) {
        // 简化版，实际实现更复杂
    }
};

THREE.RenderPass = function(scene, camera) {
    this.scene = scene;
    this.camera = camera;
    this.renderToScreen = false;
};

THREE.UnrealBloomPass = function(resolution, strength, radius, threshold) {
    this.resolution = resolution;
    this.strength = strength;
    this.radius = radius;
    this.threshold = threshold;
    this.renderToScreen = false;
};

// 导出
if (typeof module !== 'undefined') {
    module.exports = THREE;
}
