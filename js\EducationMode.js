/**
 * 教育模式模块
 * 提供分级教学、知识问答、引导教学等教育功能
 */

class EducationMode {
    constructor(viewer, moleculeLoader) {
        this.viewer = viewer;
        this.moleculeLoader = moleculeLoader;
        this.currentLevel = 'basic';
        this.learningProgress = 0;
        this.completedTopics = new Set();
        this.currentQuiz = null;
        this.notes = [];
        this.comparisonMode = false;
        this.comparedMolecules = [];
        
        this.initializeEducationMode();
    }

    /**
     * 初始化教育模式
     */
    initializeEducationMode() {
        console.log('正在初始化教育模式...');
        
        // 初始化教育级别选择
        this.initEducationLevel();
        
        // 初始化学习工具
        this.initLearningTools();
        
        // 初始化进度跟踪
        this.initProgressTracking();
        
        // 加载用户进度
        this.loadUserProgress();
        
        console.log('教育模式初始化完成');
    }

    /**
     * 初始化教育级别
     */
    initEducationLevel() {
        const levelSelect = document.getElementById('education-level');
        if (levelSelect) {
            levelSelect.addEventListener('change', (e) => {
                this.setEducationLevel(e.target.value);
            });
        }
    }

    /**
     * 设置教育级别
     */
    setEducationLevel(level) {
        this.currentLevel = level;
        console.log(`设置教育级别: ${level}`);
        
        // 根据级别调整界面和功能
        this.adjustInterfaceForLevel(level);
        
        // 更新分子信息显示
        this.updateMoleculeInfoForLevel();
    }

    /**
     * 根据教育级别调整界面
     */
    adjustInterfaceForLevel(level) {
        const levelConfig = {
            'basic': {
                showAdvanced: false,
                simplifyTerms: true,
                focusTopics: ['基本原子结构', '简单分子', '化学键']
            },
            'high-school': {
                showAdvanced: false,
                simplifyTerms: false,
                focusTopics: ['分子几何', '化学反应', '有机化学基础']
            },
            'undergraduate': {
                showAdvanced: true,
                simplifyTerms: false,
                focusTopics: ['分子轨道理论', '生物化学', '物理化学']
            },
            'graduate': {
                showAdvanced: true,
                simplifyTerms: false,
                focusTopics: ['高级结构分析', '计算化学', '研究方法']
            },
            'professional': {
                showAdvanced: true,
                simplifyTerms: false,
                focusTopics: ['专业分析工具', '研究应用', '前沿技术']
            }
        };
        
        const config = levelConfig[level];
        
        // 显示/隐藏高级功能
        const advancedFeatures = document.querySelector('.advanced-features');
        if (advancedFeatures) {
            advancedFeatures.style.display = config.showAdvanced ? 'block' : 'none';
        }
        
        // 更新焦点主题
        this.updateFocusTopics(config.focusTopics);
    }

    /**
     * 更新焦点主题
     */
    updateFocusTopics(topics) {
        console.log(`当前级别焦点主题:`, topics);
        // TODO: 根据焦点主题过滤和推荐分子
    }

    /**
     * 根据教育级别更新分子信息
     */
    updateMoleculeInfoForLevel() {
        if (!this.viewer.currentMolecule) return;
        
        const molecule = this.viewer.currentMolecule;
        const info = this.generateEducationalInfo(molecule, this.currentLevel);
        
        const infoElement = document.getElementById('molecule-info');
        if (infoElement) {
            infoElement.innerHTML = info;
        }
    }

    /**
     * 生成教育性分子信息
     */
    generateEducationalInfo(molecule, level) {
        const levelContent = {
            'basic': this.generateBasicInfo(molecule),
            'high-school': this.generateHighSchoolInfo(molecule),
            'undergraduate': this.generateUndergraduateInfo(molecule),
            'graduate': this.generateGraduateInfo(molecule),
            'professional': this.generateProfessionalInfo(molecule)
        };
        
        return levelContent[level] || this.generateBasicInfo(molecule);
    }

    /**
     * 生成基础教育信息
     */
    generateBasicInfo(molecule) {
        return `
            <h4>${molecule.name}</h4>
            <p><strong>这是什么？</strong></p>
            <p>${this.getSimpleDescription(molecule)}</p>
            <p><strong>原子数量:</strong> ${molecule.atoms.length}个</p>
            <p><strong>主要元素:</strong> ${this.getMainElements(molecule)}</p>
            <p><strong>有趣的事实:</strong> ${this.getInterestingFact(molecule)}</p>
        `;
    }

    /**
     * 生成高中化学信息
     */
    generateHighSchoolInfo(molecule) {
        return `
            <h4>${molecule.name}</h4>
            <p><strong>分子式:</strong> ${molecule.formula || '计算中...'}</p>
            <p><strong>分子几何:</strong> ${this.getMolecularGeometry(molecule)}</p>
            <p><strong>化学键类型:</strong> ${this.getBondTypes(molecule)}</p>
            <p><strong>极性:</strong> ${this.getPolarity(molecule)}</p>
            <p><strong>应用:</strong> ${this.getApplications(molecule)}</p>
        `;
    }

    /**
     * 生成大学本科信息
     */
    generateUndergraduateInfo(molecule) {
        return `
            <h4>${molecule.name}</h4>
            <p><strong>分子式:</strong> ${molecule.formula || '计算中...'}</p>
            <p><strong>分子量:</strong> ${this.calculateMolecularWeight(molecule)} g/mol</p>
            <p><strong>杂化类型:</strong> ${this.getHybridization(molecule)}</p>
            <p><strong>分子轨道:</strong> ${this.getMolecularOrbitals(molecule)}</p>
            <p><strong>反应机理:</strong> ${this.getReactionMechanism(molecule)}</p>
            <p><strong>生物学意义:</strong> ${this.getBiologicalSignificance(molecule)}</p>
        `;
    }

    /**
     * 生成研究生信息
     */
    generateGraduateInfo(molecule) {
        return `
            <h4>${molecule.name}</h4>
            <p><strong>结构分析:</strong> ${this.getStructuralAnalysis(molecule)}</p>
            <p><strong>光谱特征:</strong> ${this.getSpectralFeatures(molecule)}</p>
            <p><strong>热力学性质:</strong> ${this.getThermodynamicProperties(molecule)}</p>
            <p><strong>动力学参数:</strong> ${this.getKineticParameters(molecule)}</p>
            <p><strong>研究进展:</strong> ${this.getResearchProgress(molecule)}</p>
        `;
    }

    /**
     * 生成专业研究信息
     */
    generateProfessionalInfo(molecule) {
        return `
            <h4>${molecule.name}</h4>
            <p><strong>计算化学数据:</strong> ${this.getComputationalData(molecule)}</p>
            <p><strong>实验方法:</strong> ${this.getExperimentalMethods(molecule)}</p>
            <p><strong>结构-功能关系:</strong> ${this.getStructureFunctionRelation(molecule)}</p>
            <p><strong>最新文献:</strong> ${this.getRecentLiterature(molecule)}</p>
            <p><strong>研究应用:</strong> ${this.getResearchApplications(molecule)}</p>
        `;
    }

    /**
     * 初始化学习工具
     */
    initLearningTools() {
        // 知识问答
        const quizBtn = document.getElementById('quiz-mode');
        if (quizBtn) {
            quizBtn.addEventListener('click', () => this.startQuiz());
        }
        
        // 引导教学
        const tourBtn = document.getElementById('guided-tour');
        if (tourBtn) {
            tourBtn.addEventListener('click', () => this.startGuidedTour());
        }
        
        // 分子对比
        const comparisonBtn = document.getElementById('comparison-mode');
        if (comparisonBtn) {
            comparisonBtn.addEventListener('click', () => this.toggleComparisonMode());
        }
        
        // 笔记记录
        const notesBtn = document.getElementById('note-taking');
        if (notesBtn) {
            notesBtn.addEventListener('click', () => this.openNotesTaking());
        }
    }

    /**
     * 开始知识问答
     */
    startQuiz() {
        console.log('开始知识问答');
        
        const questions = this.generateQuestions();
        if (questions.length === 0) {
            this.showMessage('当前分子没有可用的问题');
            return;
        }
        
        this.currentQuiz = {
            questions: questions,
            currentIndex: 0,
            score: 0,
            startTime: Date.now()
        };
        
        this.showQuizQuestion();
    }

    /**
     * 生成问题
     */
    generateQuestions() {
        if (!this.viewer.currentMolecule) return [];
        
        const molecule = this.viewer.currentMolecule;
        const questions = [];
        
        // 根据教育级别生成不同难度的问题
        switch (this.currentLevel) {
            case 'basic':
                questions.push({
                    question: `${molecule.name}有多少个原子？`,
                    options: [
                        molecule.atoms.length.toString(),
                        (molecule.atoms.length + 1).toString(),
                        (molecule.atoms.length - 1).toString(),
                        (molecule.atoms.length * 2).toString()
                    ],
                    correct: 0,
                    explanation: `${molecule.name}包含${molecule.atoms.length}个原子。`
                });
                break;
                
            case 'high-school':
                questions.push({
                    question: `${molecule.name}的分子几何形状是什么？`,
                    options: ['线性', '三角形', '四面体', '八面体'],
                    correct: this.getMolecularGeometryIndex(molecule),
                    explanation: `根据VSEPR理论，${molecule.name}的几何形状是${this.getMolecularGeometry(molecule)}。`
                });
                break;
                
            // 添加更多级别的问题...
        }
        
        return questions;
    }

    /**
     * 显示问答题目
     */
    showQuizQuestion() {
        if (!this.currentQuiz) return;
        
        const quiz = this.currentQuiz;
        const question = quiz.questions[quiz.currentIndex];
        
        const quizHTML = `
            <div class="quiz-container">
                <h4>知识问答 (${quiz.currentIndex + 1}/${quiz.questions.length})</h4>
                <p class="quiz-question">${question.question}</p>
                <div class="quiz-options">
                    ${question.options.map((option, index) => 
                        `<button class="quiz-option" data-index="${index}">${option}</button>`
                    ).join('')}
                </div>
                <div class="quiz-progress">
                    <div class="quiz-progress-bar" style="width: ${(quiz.currentIndex / quiz.questions.length) * 100}%"></div>
                </div>
            </div>
        `;
        
        this.showModal('知识问答', quizHTML);
        
        // 添加选项点击事件
        document.querySelectorAll('.quiz-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.answerQuestion(parseInt(e.target.dataset.index));
            });
        });
    }

    /**
     * 回答问题
     */
    answerQuestion(selectedIndex) {
        const quiz = this.currentQuiz;
        const question = quiz.questions[quiz.currentIndex];
        
        const isCorrect = selectedIndex === question.correct;
        if (isCorrect) {
            quiz.score++;
        }
        
        // 显示解释
        this.showQuestionResult(isCorrect, question.explanation);
        
        // 延迟后显示下一题或结果
        setTimeout(() => {
            quiz.currentIndex++;
            if (quiz.currentIndex < quiz.questions.length) {
                this.showQuizQuestion();
            } else {
                this.showQuizResults();
            }
        }, 2000);
    }

    /**
     * 显示问题结果
     */
    showQuestionResult(isCorrect, explanation) {
        const resultHTML = `
            <div class="quiz-result ${isCorrect ? 'correct' : 'incorrect'}">
                <h4>${isCorrect ? '✅ 正确！' : '❌ 错误'}</h4>
                <p>${explanation}</p>
            </div>
        `;
        
        this.showModal('答题结果', resultHTML);
    }

    /**
     * 显示问答结果
     */
    showQuizResults() {
        const quiz = this.currentQuiz;
        const percentage = Math.round((quiz.score / quiz.questions.length) * 100);
        const duration = Math.round((Date.now() - quiz.startTime) / 1000);
        
        const resultHTML = `
            <div class="quiz-final-result">
                <h4>问答完成！</h4>
                <p><strong>得分:</strong> ${quiz.score}/${quiz.questions.length} (${percentage}%)</p>
                <p><strong>用时:</strong> ${duration}秒</p>
                <p><strong>评价:</strong> ${this.getQuizGrade(percentage)}</p>
                <button onclick="this.closest('.modal').remove()">关闭</button>
            </div>
        `;
        
        this.showModal('问答结果', resultHTML);
        
        // 更新学习进度
        this.updateLearningProgress(percentage);
        this.currentQuiz = null;
    }

    /**
     * 获取问答评级
     */
    getQuizGrade(percentage) {
        if (percentage >= 90) return '优秀！继续保持！';
        if (percentage >= 80) return '良好！还有提升空间';
        if (percentage >= 70) return '及格，需要更多练习';
        return '需要重新学习相关知识';
    }

    /**
     * 开始引导教学
     */
    startGuidedTour() {
        console.log('开始引导教学');
        
        const tourSteps = this.generateTourSteps();
        if (tourSteps.length === 0) {
            this.showMessage('当前分子没有可用的引导教学');
            return;
        }
        
        this.currentTourStep = 0;
        this.tourSteps = tourSteps;
        this.showTourStep();
    }

    /**
     * 生成引导教学步骤
     */
    generateTourSteps() {
        if (!this.viewer.currentMolecule) return [];
        
        const molecule = this.viewer.currentMolecule;
        const steps = [];
        
        // 基础步骤
        steps.push({
            title: '分子概览',
            content: `这是${molecule.name}，让我们一起探索它的结构。`,
            action: () => this.viewer.resetView()
        });
        
        steps.push({
            title: '原子结构',
            content: `这个分子包含${molecule.atoms.length}个原子。不同颜色代表不同的元素。`,
            action: () => this.highlightAtoms()
        });
        
        if (molecule.bonds && molecule.bonds.length > 0) {
            steps.push({
                title: '化学键',
                content: `原子之间通过${molecule.bonds.length}个化学键连接。`,
                action: () => this.highlightBonds()
            });
        }
        
        return steps;
    }

    /**
     * 显示引导教学步骤
     */
    showTourStep() {
        if (!this.tourSteps || this.currentTourStep >= this.tourSteps.length) {
            this.endGuidedTour();
            return;
        }
        
        const step = this.tourSteps[this.currentTourStep];
        
        // 执行步骤动作
        if (step.action) {
            step.action();
        }
        
        const tourHTML = `
            <div class="tour-step">
                <h4>${step.title}</h4>
                <p>${step.content}</p>
                <div class="tour-controls">
                    <button onclick="this.closest('.modal').remove()">跳过</button>
                    <button onclick="window.educationMode.nextTourStep()">下一步</button>
                </div>
                <div class="tour-progress">
                    步骤 ${this.currentTourStep + 1} / ${this.tourSteps.length}
                </div>
            </div>
        `;
        
        this.showModal('引导教学', tourHTML);
    }

    /**
     * 下一个引导步骤
     */
    nextTourStep() {
        this.currentTourStep++;
        document.querySelector('.modal')?.remove();
        this.showTourStep();
    }

    /**
     * 结束引导教学
     */
    endGuidedTour() {
        this.showMessage('引导教学完成！');
        this.currentTourStep = 0;
        this.tourSteps = null;
    }

    /**
     * 切换对比模式
     */
    toggleComparisonMode() {
        this.comparisonMode = !this.comparisonMode;
        
        const btn = document.getElementById('comparison-mode');
        if (btn) {
            btn.textContent = this.comparisonMode ? '退出对比' : '分子对比';
            btn.style.backgroundColor = this.comparisonMode ? '#dc3545' : '#28a745';
        }
        
        if (this.comparisonMode) {
            this.startComparison();
        } else {
            this.endComparison();
        }
    }

    /**
     * 开始分子对比
     */
    startComparison() {
        console.log('开始分子对比模式');
        this.showMessage('对比模式已激活，选择分子进行对比');
        
        if (this.viewer.currentMolecule) {
            this.comparedMolecules.push(this.viewer.currentMolecule);
        }
    }

    /**
     * 结束分子对比
     */
    endComparison() {
        console.log('结束分子对比模式');
        this.comparedMolecules = [];
        this.showMessage('对比模式已关闭');
    }

    /**
     * 打开笔记记录
     */
    openNotesTaking() {
        const notesHTML = `
            <div class="notes-container">
                <h4>学习笔记</h4>
                <textarea id="note-content" placeholder="在这里记录你的学习笔记..." rows="10" cols="50"></textarea>
                <div class="notes-controls">
                    <button onclick="window.educationMode.saveNote()">保存笔记</button>
                    <button onclick="window.educationMode.loadNotes()">查看历史笔记</button>
                </div>
            </div>
        `;
        
        this.showModal('学习笔记', notesHTML);
    }

    /**
     * 保存笔记
     */
    saveNote() {
        const content = document.getElementById('note-content')?.value;
        if (!content) return;
        
        const note = {
            id: Date.now(),
            content: content,
            molecule: this.viewer.currentMolecule?.name || '未知分子',
            timestamp: new Date().toLocaleString('zh-CN'),
            level: this.currentLevel
        };
        
        this.notes.push(note);
        this.saveUserProgress();
        this.showMessage('笔记已保存');
    }

    /**
     * 加载笔记
     */
    loadNotes() {
        const notesHTML = `
            <div class="notes-history">
                <h4>历史笔记</h4>
                ${this.notes.length === 0 ? '<p>暂无笔记</p>' : 
                    this.notes.map(note => `
                        <div class="note-item">
                            <h5>${note.molecule} - ${note.timestamp}</h5>
                            <p>${note.content}</p>
                        </div>
                    `).join('')
                }
            </div>
        `;
        
        this.showModal('历史笔记', notesHTML);
    }

    /**
     * 初始化进度跟踪
     */
    initProgressTracking() {
        this.updateProgressDisplay();
    }

    /**
     * 更新学习进度
     */
    updateLearningProgress(score) {
        // 根据得分更新进度
        const progressIncrement = score / 100 * 10; // 每次最多增加10%
        this.learningProgress = Math.min(100, this.learningProgress + progressIncrement);
        
        this.updateProgressDisplay();
        this.saveUserProgress();
    }

    /**
     * 更新进度显示
     */
    updateProgressDisplay() {
        const progressFill = document.getElementById('learning-progress-fill');
        const progressText = document.getElementById('learning-progress-text');
        
        if (progressFill) {
            progressFill.style.width = `${this.learningProgress}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${Math.round(this.learningProgress)}% 完成`;
        }
    }

    /**
     * 保存用户进度
     */
    saveUserProgress() {
        const progress = {
            level: this.currentLevel,
            progress: this.learningProgress,
            completedTopics: Array.from(this.completedTopics),
            notes: this.notes,
            lastUpdate: Date.now()
        };
        
        localStorage.setItem('moleculeViewer_educationProgress', JSON.stringify(progress));
    }

    /**
     * 加载用户进度
     */
    loadUserProgress() {
        const saved = localStorage.getItem('moleculeViewer_educationProgress');
        if (saved) {
            try {
                const progress = JSON.parse(saved);
                this.currentLevel = progress.level || 'basic';
                this.learningProgress = progress.progress || 0;
                this.completedTopics = new Set(progress.completedTopics || []);
                this.notes = progress.notes || [];
                
                this.updateProgressDisplay();
                console.log('用户进度已加载');
            } catch (error) {
                console.error('加载用户进度失败:', error);
            }
        }
    }

    /**
     * 显示模态框
     */
    showModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 2000; display: flex;
            align-items: center; justify-content: center;
        `;
        
        modal.innerHTML = `
            <div class="modal-content" style="
                background: white; padding: 20px; border-radius: 10px;
                max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto;
            ">
                <h3>${title}</h3>
                ${content}
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 显示消息
     */
    showMessage(message) {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 20px; right: 20px; background: #28a745;
            color: white; padding: 10px 15px; border-radius: 5px; z-index: 2000;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => toast.remove(), 3000);
    }

    // 辅助方法（简化实现）
    getSimpleDescription(molecule) { return molecule.description || '一个有趣的分子'; }
    getMainElements(molecule) { return [...new Set(molecule.atoms.map(a => a.symbol))].join(', '); }
    getInterestingFact(molecule) { return '这个分子在自然界中很常见。'; }
    getMolecularGeometry(molecule) { return '待分析'; }
    getBondTypes(molecule) { return '共价键'; }
    getPolarity(molecule) { return '待分析'; }
    getApplications(molecule) { return '多种应用'; }
    calculateMolecularWeight(molecule) { return '计算中...'; }
    getHybridization(molecule) { return '待分析'; }
    getMolecularOrbitals(molecule) { return '待分析'; }
    getReactionMechanism(molecule) { return '待分析'; }
    getBiologicalSignificance(molecule) { return '具有重要生物学意义'; }
    getStructuralAnalysis(molecule) { return '详细结构分析'; }
    getSpectralFeatures(molecule) { return '光谱特征分析'; }
    getThermodynamicProperties(molecule) { return '热力学性质'; }
    getKineticParameters(molecule) { return '动力学参数'; }
    getResearchProgress(molecule) { return '最新研究进展'; }
    getComputationalData(molecule) { return '计算化学数据'; }
    getExperimentalMethods(molecule) { return '实验方法'; }
    getStructureFunctionRelation(molecule) { return '结构-功能关系'; }
    getRecentLiterature(molecule) { return '最新文献'; }
    getResearchApplications(molecule) { return '研究应用'; }
    getMolecularGeometryIndex(molecule) { return 0; }
    highlightAtoms() { console.log('高亮原子'); }
    highlightBonds() { console.log('高亮化学键'); }
}

// 导出类
window.EducationMode = EducationMode;
