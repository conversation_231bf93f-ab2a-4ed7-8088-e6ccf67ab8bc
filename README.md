# 分子生物学可视化项目

## 项目概述
这是一个基于Three.js的分子生物学可视化项目，可以展示各种分子的3D结构和动画效果。

## 最近修复的问题

### 1. 编码问题修复
- ✅ 修复了批处理文件的中文路径编码问题
- ✅ 添加了UTF-8编码支持 (`chcp 65001`)
- ✅ 清理了debug.log中的乱码内容

### 2. Three.js库优化
- ✅ 优化了Three.js加载策略，优先使用CDN
- ✅ 添加了多个CDN备用源，提高加载成功率
- ✅ 改进了错误处理和用户提示

### 3. WebGL兼容性改进
- ✅ 添加了WebGL支持检测
- ✅ 改进了渲染器初始化错误处理
- ✅ 提供了更好的错误提示信息

### 4. 用户体验优化
- ✅ 添加了favicon图标，解决404错误
- ✅ 改进了加载进度显示
- ✅ 优化了错误信息展示

## 使用方法

### 直接打开（推荐）
1. 直接双击 `index.html` 文件
2. 程序会在默认浏览器中打开
3. 开始探索分子世界！

### 使用本地服务器（可选）
如果需要完整功能（如PDB加载），可以启动本地服务器：
1. 在项目目录打开命令行
2. 运行：`python -m http.server 8000`
3. 在浏览器中访问：`http://localhost:8000`

## 项目结构
```
分子生物学可视化/
├── index.html              # 主页面
├── css/
│   └── styles.css          # 样式文件
├── js/
│   ├── main.js            # 主程序入口
│   ├── MoleculeViewer.js  # 分子可视化器核心
│   ├── MoleculeLoader.js  # 分子数据加载器
│   ├── ReactionEngine.js  # 化学反应引擎 🧪
│   ├── controls.js        # 用户界面控制
│   ├── FallbackRenderer.js # 备用渲染器
│   ├── molecules.js       # 分子数据定义(47种分子)
│   └── lib/               # 第三方库
│       ├── three.min.js   # Three.js 3D库
│       ├── OrbitControls.js # 轨道控制器
│       └── PDBLoader.js   # PDB文件加载器
├── 分子扩展总结.md         # 分子扩展文档
├── 化学反应功能扩展总结.md  # 化学反应功能文档
├── favicon.svg            # 网站图标
└── README.md             # 项目说明
```

## 技术特性
- 🔬 支持47种分子结构可视化
- 🧪 **化学反应3D模拟** - 全新功能！
  - 酸碱中和反应动画
  - 甲烷燃烧反应过程
  - 酯化反应机理展示
  - 肽键形成生物反应
- 🎮 交互式3D控制（旋转、缩放、平移）
- 🎨 多种显示模式（球棍模型、空间填充等）
- 🎬 分子动画效果（振动、旋转等）
- ⚡ 可调节反应速度（0.1x - 3.0x）
- 📊 实时反应进度显示
- 📱 响应式设计，支持移动设备
- 🔧 自动错误检测和恢复

## 浏览器兼容性
- ✅ Chrome (推荐)
- ✅ Firefox
- ✅ Safari
- ⚠️ Edge (需要启用WebGL)
- ❌ Internet Explorer (不支持)

## 故障排除

### 如果页面无法加载：
1. 检查网络连接
2. 尝试刷新页面
3. 检查浏览器是否支持WebGL
4. 查看浏览器控制台的错误信息

### 如果3D渲染失败：
1. 确保浏览器支持WebGL
2. 更新显卡驱动
3. 尝试使用其他浏览器
4. 检查硬件加速是否启用

## 开发说明
项目使用了以下主要技术：
- **Three.js**: 3D图形渲染
- **WebGL**: 硬件加速渲染
- **HTML5 Canvas**: 2D备用渲染
- **CSS3**: 现代样式和动画
- **JavaScript ES6+**: 现代JavaScript特性

## 更新日志
- **v2.0** (2025-01-27): 🧪 **重大更新 - 化学反应功能**
  - 新增化学反应3D模拟系统
  - 新增10种分子（总计47种）
  - 新增ReactionEngine反应引擎
  - 新增4种化学反应类型
  - 新增反应控制界面
  - 清理项目结构，移除冗余文件
- **v1.4** (2025-01-27): 修复编码问题，优化Three.js加载，改进错误处理
- **v1.3** (之前): 基础功能实现