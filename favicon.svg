<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <!-- 分子结构图标 -->
  <defs>
    <style>
      .atom { fill: #3498db; stroke: #2980b9; stroke-width: 1; }
      .bond { stroke: #34495e; stroke-width: 2; fill: none; }
      .atom-o { fill: #e74c3c; }
      .atom-h { fill: #ecf0f1; stroke: #bdc3c7; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="32" height="32" fill="#2c3e50" rx="4"/>
  
  <!-- 分子键 -->
  <line class="bond" x1="8" y1="16" x2="16" y2="10"/>
  <line class="bond" x1="8" y1="16" x2="16" y2="22"/>
  <line class="bond" x1="16" y1="10" x2="24" y2="16"/>
  
  <!-- 原子 -->
  <circle class="atom atom-o" cx="8" cy="16" r="3"/>
  <circle class="atom atom-h" cx="16" cy="10" r="2.5"/>
  <circle class="atom atom-h" cx="16" cy="22" r="2.5"/>
  <circle class="atom" cx="24" cy="16" r="3"/>
</svg>